# Timesheet 365 - SPFx Time Tracking Solution

## Summary

Timesheet 365 is a comprehensive time tracking solution built as a SharePoint Framework (SPFx) web part. It provides seamless integration within the Microsoft 365 ecosystem, offering project management, time tracking, cost management, reporting, and approval workflows - all while keeping data within your Microsoft 365 tenant for security and compliance.

![Timesheet 365 Overview](./docs/images/timesheet-overview.png)

## Used SharePoint Framework Version

![version](https://img.shields.io/badge/version-1.21.1-green.svg)

## Applies to

- [SharePoint Framework](https://aka.ms/spfx)
- [Microsoft 365 tenant](https://docs.microsoft.com/en-us/sharepoint/dev/spfx/set-up-your-developer-tenant)

> Get your own free development tenant by subscribing to [Microsoft 365 developer program](http://aka.ms/o365devprogram)

## Prerequisites

> Any special pre-requisites?

## Solution

| Solution                    | Author(s)                                               |
| --------------------------- | ------------------------------------------------------- |
| Timesheet 365 SPFx         | EasyGuide Team (EasyGuide, @easyguide_team)           |

## Version history

| Version | Date             | Comments                                                |
| ------- | ---------------- | ------------------------------------------------------- |
| 1.0     | July 7, 2024     | Initial release - Core functionality implementation     |
| 0.0.1   | July 7, 2024     | Development version - Framework and structure setup    |

## Disclaimer

**THIS CODE IS PROVIDED _AS IS_ WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING ANY IMPLIED WARRANTIES OF FITNESS FOR A PARTICULAR PURPOSE, MERCHANTABILITY, OR NON-INFRINGEMENT.**

---

## Minimal Path to Awesome

### For Development:
- Clone this repository
- Ensure that you are at the solution folder
- In the command line run:
  - **npm install**
  - **gulp serve**

### For Production Deployment:
- Download the latest release package: `time-sheet-tracking-spfx.sppkg`
- Upload to your SharePoint App Catalog
- Add the web part to your SharePoint page or Teams channel
- Configure SharePoint lists (will be auto-created on first use)
- Assign user roles and permissions

### Initial Setup:
1. **Admin Configuration**: First user with admin permissions sets up projects and user roles
2. **SharePoint Lists**: The solution automatically creates required SharePoint lists:
   - Projects (for project management)
   - Timesheets (for timesheet storage)
3. **User Permissions**: Configure user roles in SharePoint groups or through the admin interface
4. **Teams Integration**: Add as a Teams tab for seamless collaboration

## Features

Timesheet 365 provides a comprehensive set of features for modern organizations:

### Core Functionality
- **Multi-role Support**: Administrator, Program Manager, Project Manager, Coordinator, Project Observer, and User roles
- **Project & Task Management**: Create, edit, and manage unlimited projects with custom activities
- **Time Tracking**: Multiple time entry methods (manual entry, start/stop timer, login/logout timestamps)
- **Weekly & Daily Views**: Intuitive timesheet interfaces for easy time entry and review
- **Cost Management**: Configure hourly rates, onsite rates, and project-specific pricing

### Advanced Features
- **Bulk Data Operations**: CSV upload for project activities and data management
- **Document Storage**: Project-related document storage within SharePoint
- **Custom Fields**: Extend projects and tasks with custom columns from SharePoint user profiles
- **Approval Workflows**: Complete timesheet submission and approval process with delegation
- **Comprehensive Reporting**: Daily, weekly, monthly reports with Power BI integration
- **Email Notifications**: Automated notifications for submissions, approvals, and reminders

### Technical Features
- **SharePoint Integration**: All data stored in SharePoint lists within your tenant
- **Teams Compatibility**: Deploy as SharePoint web part or Teams tab
- **Responsive Design**: Optimized for desktop and mobile devices
- **Role-based Security**: Leverages SharePoint permissions model
- **Modern UI**: Built with Fluent UI React components

> Notice that better pictures and documentation will increase the sample usage and the value you are providing for others. Thanks for your submissions advance.

> Share your web part with others through Microsoft 365 Patterns and Practices program to get visibility and exposure. More details on the community, open-source projects and other activities from http://aka.ms/m365pnp.

## References

- [Getting started with SharePoint Framework](https://docs.microsoft.com/en-us/sharepoint/dev/spfx/set-up-your-developer-tenant)
- [Building for Microsoft teams](https://docs.microsoft.com/en-us/sharepoint/dev/spfx/build-for-teams-overview)
- [Use Microsoft Graph in your solution](https://docs.microsoft.com/en-us/sharepoint/dev/spfx/web-parts/get-started/using-microsoft-graph-apis)
- [Publish SharePoint Framework applications to the Marketplace](https://docs.microsoft.com/en-us/sharepoint/dev/spfx/publish-to-marketplace-overview)
- [Microsoft 365 Patterns and Practices](https://aka.ms/m365pnp) - Guidance, tooling, samples and open-source controls for your Microsoft 365 development
