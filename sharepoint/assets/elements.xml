<?xml version="1.0" encoding="utf-8"?>
<Elements xmlns="http://schemas.microsoft.com/sharepoint/">
  
  <!-- Projects List -->
  <ListInstance 
    Title="Projects"
    Description="Stores project information and configuration for Timesheet 365"
    TemplateType="100"
    FeatureId="00bfea71-de22-43b2-a848-c05709900100"
    Url="Lists/Projects"
    OnQuickLaunch="TRUE">
    
    <Data>
      <Rows>
        <!-- Sample data can be added here if needed -->
      </Rows>
    </Data>
  </ListInstance>

  <!-- Timesheets List -->
  <ListInstance 
    Title="Timesheets"
    Description="Stores weekly timesheet records for Timesheet 365"
    TemplateType="100"
    FeatureId="00bfea71-de22-43b2-a848-c05709900100"
    Url="Lists/Timesheets"
    OnQuickLaunch="TRUE">
    
    <Data>
      <Rows>
        <!-- Sample data can be added here if needed -->
      </Rows>
    </Data>
  </ListInstance>

  <!-- Activities List -->
  <ListInstance 
    Title="Activities"
    Description="Stores project activities and tasks for Timesheet 365"
    TemplateType="100"
    FeatureId="00bfea71-de22-43b2-a848-c05709900100"
    Url="Lists/Activities"
    OnQuickLaunch="TRUE">
    
    <Data>
      <Rows>
        <!-- Sample data can be added here if needed -->
      </Rows>
    </Data>
  </ListInstance>

  <!-- UserProfiles List -->
  <ListInstance 
    Title="UserProfiles"
    Description="Stores user-specific settings and roles for Timesheet 365"
    TemplateType="100"
    FeatureId="00bfea71-de22-43b2-a848-c05709900100"
    Url="Lists/UserProfiles"
    OnQuickLaunch="TRUE">
    
    <Data>
      <Rows>
        <!-- Sample data can be added here if needed -->
      </Rows>
    </Data>
  </ListInstance>

  <!-- TimeEntries List (Optional) -->
  <ListInstance 
    Title="TimeEntries"
    Description="Individual time entries for detailed tracking in Timesheet 365"
    TemplateType="100"
    FeatureId="00bfea71-de22-43b2-a848-c05709900100"
    Url="Lists/TimeEntries"
    OnQuickLaunch="FALSE">
    
    <Data>
      <Rows>
        <!-- Sample data can be added here if needed -->
      </Rows>
    </Data>
  </ListInstance>

</Elements>
