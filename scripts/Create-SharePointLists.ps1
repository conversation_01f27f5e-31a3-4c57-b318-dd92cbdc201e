# SharePoint Lists Creation Script for Timesheet 365
# This script creates all required SharePoint lists for the Timesheet 365 application

param(
    [Parameter(Mandatory=$true)]
    [string]$SiteUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$Username,
    
    [Parameter(Mandatory=$false)]
    [string]$Password
)

# Import SharePoint PnP PowerShell module
if (!(Get-Module -ListAvailable -Name PnP.PowerShell)) {
    Write-Host "Installing PnP.PowerShell module..." -ForegroundColor Yellow
    Install-Module -Name PnP.PowerShell -Force -AllowClobber
}

Import-Module PnP.PowerShell

# Connect to SharePoint
Write-Host "Connecting to SharePoint site: $SiteUrl" -ForegroundColor Green
try {
    if ($Username -and $Password) {
        $SecurePassword = ConvertTo-SecureString $Password -AsPlainText -Force
        $Credential = New-Object System.Management.Automation.PSCredential($Username, $SecurePassword)
        Connect-PnPOnline -Url $SiteUrl -Credentials $Credential
    } else {
        Connect-PnPOnline -Url $SiteUrl -Interactive
    }
    Write-Host "Successfully connected to SharePoint!" -ForegroundColor Green
} catch {
    Write-Error "Failed to connect to SharePoint: $($_.Exception.Message)"
    exit 1
}

# Function to create a list if it doesn't exist
function Create-ListIfNotExists {
    param(
        [string]$ListName,
        [string]$Description,
        [string]$Template = "GenericList"
    )
    
    $existingList = Get-PnPList -Identity $ListName -ErrorAction SilentlyContinue
    if ($existingList) {
        Write-Host "List '$ListName' already exists. Skipping creation." -ForegroundColor Yellow
        return $existingList
    }
    
    Write-Host "Creating list: $ListName" -ForegroundColor Blue
    $list = New-PnPList -Title $ListName -Description $Description -Template $Template
    Write-Host "List '$ListName' created successfully!" -ForegroundColor Green
    return $list
}

# Function to add a field if it doesn't exist
function Add-FieldIfNotExists {
    param(
        [string]$ListName,
        [string]$FieldName,
        [string]$FieldType,
        [hashtable]$FieldProperties = @{}
    )
    
    $existingField = Get-PnPField -List $ListName -Identity $FieldName -ErrorAction SilentlyContinue
    if ($existingField) {
        Write-Host "Field '$FieldName' already exists in list '$ListName'. Skipping." -ForegroundColor Yellow
        return
    }
    
    Write-Host "Adding field '$FieldName' to list '$ListName'" -ForegroundColor Blue
    
    switch ($FieldType) {
        "Text" {
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type Text @FieldProperties
        }
        "Note" {
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type Note @FieldProperties
        }
        "Number" {
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type Number @FieldProperties
        }
        "DateTime" {
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type DateTime @FieldProperties
        }
        "Boolean" {
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type Boolean @FieldProperties
        }
        "User" {
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type User @FieldProperties
        }
        "UserMulti" {
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type UserMulti @FieldProperties
        }
        "Choice" {
            $choices = $FieldProperties["Choices"]
            Add-PnPField -List $ListName -DisplayName $FieldName -InternalName $FieldName -Type Choice -Choices $choices @($FieldProperties.GetEnumerator() | Where-Object {$_.Key -ne "Choices"})
        }
    }
}

# Create Projects List
Write-Host "`n=== Creating Projects List ===" -ForegroundColor Cyan
Create-ListIfNotExists -ListName "Projects" -Description "Stores project information and configuration"

Add-FieldIfNotExists -ListName "Projects" -FieldName "Description" -FieldType "Note"
Add-FieldIfNotExists -ListName "Projects" -FieldName "Client" -FieldType "Text"
Add-FieldIfNotExists -ListName "Projects" -FieldName "Company" -FieldType "Text"
Add-FieldIfNotExists -ListName "Projects" -FieldName "StartDate" -FieldType "DateTime"
Add-FieldIfNotExists -ListName "Projects" -FieldName "EndDate" -FieldType "DateTime"
Add-FieldIfNotExists -ListName "Projects" -FieldName "Status" -FieldType "Choice" -FieldProperties @{
    Choices = @("Active", "Completed", "OnHold", "Cancelled")
    Required = $true
}
Add-FieldIfNotExists -ListName "Projects" -FieldName "HourlyRate" -FieldType "Number" -FieldProperties @{Min = 0}
Add-FieldIfNotExists -ListName "Projects" -FieldName "OnsiteRate" -FieldType "Number" -FieldProperties @{Min = 0}
Add-FieldIfNotExists -ListName "Projects" -FieldName "ProjectManager" -FieldType "User"
Add-FieldIfNotExists -ListName "Projects" -FieldName "TeamMembers" -FieldType "UserMulti"
Add-FieldIfNotExists -ListName "Projects" -FieldName "CustomFields" -FieldType "Note"

# Create Timesheets List
Write-Host "`n=== Creating Timesheets List ===" -ForegroundColor Cyan
Create-ListIfNotExists -ListName "Timesheets" -Description "Stores weekly timesheet records"

Add-FieldIfNotExists -ListName "Timesheets" -FieldName "UserId" -FieldType "Text" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "UserName" -FieldType "Text" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "WeekStartDate" -FieldType "DateTime" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "WeekEndDate" -FieldType "DateTime" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "TimeEntries" -FieldType "Note" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "TotalHours" -FieldType "Number" -FieldProperties @{Required = $true; Min = 0}
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "Status" -FieldType "Choice" -FieldProperties @{
    Choices = @("Draft", "Submitted", "Approved", "Rejected")
    Required = $true
}
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "SubmittedDate" -FieldType "DateTime"
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "ApprovedDate" -FieldType "DateTime"
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "ApprovedBy" -FieldType "User"
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "RejectedDate" -FieldType "DateTime"
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "RejectedBy" -FieldType "User"
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "RejectionReason" -FieldType "Note"
Add-FieldIfNotExists -ListName "Timesheets" -FieldName "Comments" -FieldType "Note"

# Create Activities List
Write-Host "`n=== Creating Activities List ===" -ForegroundColor Cyan
Create-ListIfNotExists -ListName "Activities" -Description "Stores project activities and tasks"

Add-FieldIfNotExists -ListName "Activities" -FieldName "ProjectId" -FieldType "Number" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "Activities" -FieldName "Description" -FieldType "Note"
Add-FieldIfNotExists -ListName "Activities" -FieldName "EstimatedHours" -FieldType "Number" -FieldProperties @{Min = 0}
Add-FieldIfNotExists -ListName "Activities" -FieldName "ActualHours" -FieldType "Number" -FieldProperties @{Min = 0}
Add-FieldIfNotExists -ListName "Activities" -FieldName "Status" -FieldType "Choice" -FieldProperties @{
    Choices = @("NotStarted", "InProgress", "Completed", "OnHold")
    Required = $true
}
Add-FieldIfNotExists -ListName "Activities" -FieldName "AssignedTo" -FieldType "UserMulti"

# Create UserProfiles List
Write-Host "`n=== Creating UserProfiles List ===" -ForegroundColor Cyan
Create-ListIfNotExists -ListName "UserProfiles" -Description "Stores user-specific settings and roles"

Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "UserId" -FieldType "Text" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "Email" -FieldType "Text" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "Role" -FieldType "Choice" -FieldProperties @{
    Choices = @("Administrator", "ProgramManager", "ProjectManager", "Coordinator", "ProjectObserver", "User")
    Required = $true
}
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "HourlyRate" -FieldType "Number" -FieldProperties @{Min = 0}
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "OnsiteRate" -FieldType "Number" -FieldProperties @{Min = 0}
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "MaxDailyHours" -FieldType "Number" -FieldProperties @{Min = 1; Max = 24}
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "AllowWeekends" -FieldType "Boolean"
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "Manager" -FieldType "User"
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "Department" -FieldType "Text"
Add-FieldIfNotExists -ListName "UserProfiles" -FieldName "IsActive" -FieldType "Boolean" -FieldProperties @{Required = $true}

# Create TimeEntries List (Optional)
Write-Host "`n=== Creating TimeEntries List ===" -ForegroundColor Cyan
Create-ListIfNotExists -ListName "TimeEntries" -Description "Individual time entries for detailed tracking"

Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "TimesheetId" -FieldType "Number" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "ProjectId" -FieldType "Number" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "ActivityId" -FieldType "Number"
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "UserId" -FieldType "Text" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "Date" -FieldType "DateTime" -FieldProperties @{Required = $true}
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "Hours" -FieldType "Number" -FieldProperties @{Required = $true; Min = 0}
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "WorkType" -FieldType "Choice" -FieldProperties @{
    Choices = @("Office", "Onsite", "WFH")
    Required = $true
}
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "Description" -FieldType "Note"
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "StartTime" -FieldType "DateTime"
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "EndTime" -FieldType "DateTime"
Add-FieldIfNotExists -ListName "TimeEntries" -FieldName "IsOvertime" -FieldType "Boolean"

Write-Host "`n=== SharePoint Lists Creation Completed! ===" -ForegroundColor Green
Write-Host "All required lists have been created successfully." -ForegroundColor Green
Write-Host "Please review the lists and configure permissions as needed." -ForegroundColor Yellow

# Disconnect from SharePoint
Disconnect-PnPOnline
Write-Host "Disconnected from SharePoint." -ForegroundColor Blue
