export class DateUtils {
  /**
   * Get the start of the week (Monday) for a given date
   */
  public static getWeekStart(date: Date): Date {
    const d = new Date(date.getTime());
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    d.setDate(diff);
    return d;
  }

  /**
   * Get the end of the week (Sunday) for a given date
   */
  public static getWeekEnd(date: Date): Date {
    const weekStart = DateUtils.getWeekStart(date);
    const weekEnd = new Date(weekStart.getTime());
    weekEnd.setDate(weekStart.getDate() + 6);
    return weekEnd;
  }

  /**
   * Get an array of dates for the week containing the given date
   */
  public static getWeekDates(date: Date): Date[] {
    const weekStart = DateUtils.getWeekStart(date);
    const dates: Date[] = [];
    
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(weekStart.getTime());
      currentDate.setDate(weekStart.getDate() + i);
      dates.push(currentDate);
    }
    
    return dates;
  }

  /**
   * Format date as YYYY-MM-DD
   */
  public static formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Format date for display (e.g., "Mon, Jan 15")
   */
  public static formatDisplayDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  }

  /**
   * Format date for week range display (e.g., "Jan 15 - Jan 21, 2024")
   */
  public static formatWeekRange(startDate: Date, endDate: Date): string {
    const startMonth = startDate.toLocaleDateString('en-US', { month: 'short' });
    const endMonth = endDate.toLocaleDateString('en-US', { month: 'short' });
    const startDay = startDate.getDate();
    const endDay = endDate.getDate();
    const year = endDate.getFullYear();

    if (startMonth === endMonth) {
      return `${startMonth} ${startDay} - ${endDay}, ${year}`;
    } else {
      return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
    }
  }

  /**
   * Check if a date is today
   */
  public static isToday(date: Date): boolean {
    const today = new Date();
    return DateUtils.formatDate(date) === DateUtils.formatDate(today);
  }

  /**
   * Check if a date is in the current week
   */
  public static isCurrentWeek(date: Date): boolean {
    const today = new Date();
    const currentWeekStart = DateUtils.getWeekStart(today);
    const currentWeekEnd = DateUtils.getWeekEnd(today);
    
    return date >= currentWeekStart && date <= currentWeekEnd;
  }

  /**
   * Get the difference in days between two dates
   */
  public static getDaysDifference(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Check if a date is a weekend (Saturday or Sunday)
   */
  public static isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }

  /**
   * Add days to a date
   */
  public static addDays(date: Date, days: number): Date {
    const result = new Date(date.getTime());
    result.setDate(result.getDate() + days);
    return result;
  }

  /**
   * Add weeks to a date
   */
  public static addWeeks(date: Date, weeks: number): Date {
    return DateUtils.addDays(date, weeks * 7);
  }

  /**
   * Get the current week number of the year
   */
  public static getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  }

  /**
   * Parse time string (HH:MM) to hours as decimal
   */
  public static parseTimeToHours(timeString: string): number {
    if (!timeString) return 0;
    
    const parts = timeString.split(':');
    if (parts.length !== 2) return 0;
    
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    
    return hours + (minutes / 60);
  }

  /**
   * Format hours as time string (HH:MM)
   */
  public static formatHoursAsTime(hours: number): string {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    
    return `${wholeHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }
}