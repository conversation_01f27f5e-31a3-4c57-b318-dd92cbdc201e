import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  Text,
  Stack,
  DetailsList,
  IColumn,
  DetailsListLayoutMode,
  SelectionMode,
  DefaultButton,
  IconButton,
  TooltipHost,
  MessageBar,
  MessageBarType
} from '@fluentui/react';
import { ITimeEntry, IProject, IUser, WorkType } from '../../models';
import { CostCalculator, ICostCalculation, ICostBreakdown } from './CostCalculator';
import styles from '../QtsTimesheetTracker.module.scss';

interface ICostSummaryProps {
  timeEntries: ITimeEntry[];
  projects: IProject[];
  users?: IUser[];
  currentUserId?: string;
  showBreakdown?: boolean;
  showExport?: boolean;
  title?: string;
  defaultHourlyRate?: number;
  defaultOnsiteRate?: number;
}

const CostSummary: React.FC<ICostSummaryProps> = ({
  timeEntries,
  projects,
  users = [],
  currentUserId,
  showBreakdown = true,
  showExport = true,
  title = 'Cost Summary',
  defaultHourlyRate = 50,
  defaultOnsiteRate = 75
}) => {
  const [calculation, setCalculation] = useState<ICostCalculation | null>(null);
  const [calculator, setCalculator] = useState<CostCalculator | null>(null);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);

  useEffect(() => {
    const calc = new CostCalculator(projects, users, defaultHourlyRate, defaultOnsiteRate);
    setCalculator(calc);
    
    const result = calc.calculateCosts(timeEntries, currentUserId);
    setCalculation(result);
  }, [timeEntries, projects, users, currentUserId, defaultHourlyRate, defaultOnsiteRate]);

  const exportCostReport = (format: 'csv' | 'json'): void => {
    if (!calculation || !calculator) return;

    try {
      const reportData = calculator.exportCostReport(calculation, format);
      const blob = new Blob([reportData], { 
        type: format === 'csv' ? 'text/csv' : 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `cost-report-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setMessage({
        text: `Cost report exported successfully as ${format.toUpperCase()}`,
        type: MessageBarType.success
      });
    } catch (error) {
      setMessage({
        text: 'Failed to export cost report',
        type: MessageBarType.error
      });
      console.error('Export error:', error);
    }
  };

  const getWorkTypeIcon = (workType: WorkType): string => {
    switch (workType) {
      case WorkType.Office:
        return 'BuildingOffice';
      case WorkType.Onsite:
        return 'LocationDot';
      case WorkType.WFH:
        return 'Home';
      default:
        return 'Work';
    }
  };

  const getWorkTypeColor = (workType: WorkType): string => {
    switch (workType) {
      case WorkType.Office:
        return '#0078d4';
      case WorkType.Onsite:
        return '#d83b01';
      case WorkType.WFH:
        return '#107c10';
      default:
        return '#605e5c';
    }
  };

  const breakdownColumns: IColumn[] = [
    {
      key: 'project',
      name: 'Project',
      fieldName: 'projectTitle',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'workType',
      name: 'Work Type',
      fieldName: 'workType',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: ICostBreakdown) => (
        <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 5 }}>
          <IconButton
            iconProps={{ 
              iconName: getWorkTypeIcon(item.workType),
              style: { color: getWorkTypeColor(item.workType) }
            }}
            styles={{ root: { height: 'auto', width: 'auto' } }}
          />
          <Text variant="small">{item.workType}</Text>
        </Stack>
      )
    },
    {
      key: 'hours',
      name: 'Hours',
      fieldName: 'hours',
      minWidth: 60,
      maxWidth: 80,
      isResizable: true,
      onRender: (item: ICostBreakdown) => (
        <Text variant="small">{item.hours.toFixed(2)}</Text>
      )
    },
    {
      key: 'rate',
      name: 'Rate',
      fieldName: 'rate',
      minWidth: 60,
      maxWidth: 80,
      isResizable: true,
      onRender: (item: ICostBreakdown) => (
        <Text variant="small">${item.rate.toFixed(2)}</Text>
      )
    },
    {
      key: 'cost',
      name: 'Cost',
      fieldName: 'cost',
      minWidth: 80,
      maxWidth: 100,
      isResizable: true,
      onRender: (item: ICostBreakdown) => (
        <Text variant="small" style={{ fontWeight: 'bold' }}>
          ${item.cost.toFixed(2)}
        </Text>
      )
    }
  ];

  if (!calculation) {
    return <div>Loading cost calculation...</div>;
  }

  return (
    <div className={styles.costSummary}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <Stack horizontal horizontalAlign="space-between" verticalAlign="center">
        <Text variant="large" block style={{ fontWeight: 'bold' }}>
          {title}
        </Text>
        {showExport && (
          <Stack horizontal tokens={{ childrenGap: 5 }}>
            <TooltipHost content="Export as CSV">
              <DefaultButton
                iconProps={{ iconName: 'ExcelDocument' }}
                onClick={() => exportCostReport('csv')}
                text="CSV"
              />
            </TooltipHost>
            <TooltipHost content="Export as JSON">
              <DefaultButton
                iconProps={{ iconName: 'FileCode' }}
                onClick={() => exportCostReport('json')}
                text="JSON"
              />
            </TooltipHost>
          </Stack>
        )}
      </Stack>

      {/* Summary Cards */}
      <div className={styles.summaryCards}>
        <div className={styles.summaryCard}>
          <div className={styles.cardTitle}>Total Hours</div>
          <div className={styles.cardValue}>{calculation.totalHours.toFixed(2)}</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.cardTitle}>Total Cost</div>
          <div className={styles.cardValue}>${calculation.totalCost.toFixed(2)}</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.cardTitle}>Average Rate</div>
          <div className={styles.cardValue}>
            ${calculation.totalHours > 0 ? (calculation.totalCost / calculation.totalHours).toFixed(2) : '0.00'}
          </div>
        </div>
      </div>

      {/* Work Type Breakdown */}
      <Stack tokens={{ childrenGap: 15 }}>
        <Text variant="medium" style={{ fontWeight: 'bold' }}>
          Work Type Breakdown
        </Text>
        
        <div className={styles.workTypeBreakdown}>
          {calculation.officeHours > 0 && (
            <div className={styles.workTypeItem}>
              <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 10 }}>
                <IconButton
                  iconProps={{ 
                    iconName: getWorkTypeIcon(WorkType.Office),
                    style: { color: getWorkTypeColor(WorkType.Office) }
                  }}
                />
                <Stack>
                  <Text variant="medium">Office Work</Text>
                  <Text variant="small" style={{ color: '#605e5c' }}>
                    {calculation.officeHours.toFixed(2)} hours • ${calculation.officeCost.toFixed(2)}
                  </Text>
                </Stack>
              </Stack>
            </div>
          )}
          
          {calculation.onsiteHours > 0 && (
            <div className={styles.workTypeItem}>
              <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 10 }}>
                <IconButton
                  iconProps={{ 
                    iconName: getWorkTypeIcon(WorkType.Onsite),
                    style: { color: getWorkTypeColor(WorkType.Onsite) }
                  }}
                />
                <Stack>
                  <Text variant="medium">Onsite Work</Text>
                  <Text variant="small" style={{ color: '#605e5c' }}>
                    {calculation.onsiteHours.toFixed(2)} hours • ${calculation.onsiteCost.toFixed(2)}
                  </Text>
                </Stack>
              </Stack>
            </div>
          )}
          
          {calculation.wfhHours > 0 && (
            <div className={styles.workTypeItem}>
              <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 10 }}>
                <IconButton
                  iconProps={{ 
                    iconName: getWorkTypeIcon(WorkType.WFH),
                    style: { color: getWorkTypeColor(WorkType.WFH) }
                  }}
                />
                <Stack>
                  <Text variant="medium">Work From Home</Text>
                  <Text variant="small" style={{ color: '#605e5c' }}>
                    {calculation.wfhHours.toFixed(2)} hours • ${calculation.wfhCost.toFixed(2)}
                  </Text>
                </Stack>
              </Stack>
            </div>
          )}
        </div>
      </Stack>

      {/* Detailed Breakdown */}
      {showBreakdown && calculation.breakdown.length > 0 && (
        <Stack tokens={{ childrenGap: 15 }}>
          <Text variant="medium" style={{ fontWeight: 'bold' }}>
            Detailed Breakdown
          </Text>
          
          <DetailsList
            items={calculation.breakdown}
            columns={breakdownColumns}
            layoutMode={DetailsListLayoutMode.justified}
            selectionMode={SelectionMode.none}
            isHeaderVisible={true}
            className={styles.detailsList}
          />
        </Stack>
      )}
    </div>
  );
};

export default CostSummary;
