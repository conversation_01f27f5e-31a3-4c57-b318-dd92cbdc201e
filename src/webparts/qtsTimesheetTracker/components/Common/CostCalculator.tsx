import { ITimeEntry, IProject, WorkType, IUser } from '../../models';

export interface ICostCalculation {
  totalHours: number;
  totalCost: number;
  officeHours: number;
  officeCost: number;
  onsiteHours: number;
  onsiteCost: number;
  wfhHours: number;
  wfhCost: number;
  breakdown: ICostBreakdown[];
}

export interface ICostBreakdown {
  projectId: number;
  projectTitle: string;
  workType: WorkType;
  hours: number;
  rate: number;
  cost: number;
}

export interface IRateConfiguration {
  projectRates: Map<number, { hourlyRate: number; onsiteRate: number }>;
  userRates: Map<string, { hourlyRate: number; onsiteRate: number }>;
  defaultRates: { hourlyRate: number; onsiteRate: number };
}

export class CostCalculator {
  private rateConfig: IRateConfiguration;

  constructor(
    projects: IProject[],
    users: IUser[],
    defaultHourlyRate: number = 50,
    defaultOnsiteRate: number = 75
  ) {
    this.rateConfig = {
      projectRates: new Map(),
      userRates: new Map(),
      defaultRates: { hourlyRate: defaultHourlyRate, onsiteRate: defaultOnsiteRate }
    };

    // Initialize project rates
    projects.forEach(project => {
      if (project.id && (project.hourlyRate || project.onsiteRate)) {
        this.rateConfig.projectRates.set(project.id, {
          hourlyRate: project.hourlyRate || defaultHourlyRate,
          onsiteRate: project.onsiteRate || defaultOnsiteRate
        });
      }
    });

    // Initialize user rates
    users.forEach(user => {
      if (user.id && (user.hourlyRate || user.onsiteRate)) {
        this.rateConfig.userRates.set(user.id, {
          hourlyRate: user.hourlyRate || defaultHourlyRate,
          onsiteRate: user.onsiteRate || defaultOnsiteRate
        });
      }
    });
  }

  public calculateCosts(timeEntries: ITimeEntry[], userId?: string): ICostCalculation {
    const breakdown: ICostBreakdown[] = [];
    let totalHours = 0;
    let totalCost = 0;
    let officeHours = 0;
    let officeCost = 0;
    let onsiteHours = 0;
    let onsiteCost = 0;
    let wfhHours = 0;
    let wfhCost = 0;

    // Group entries by project and work type
    const groupedEntries = new Map<string, ITimeEntry[]>();
    timeEntries.forEach(entry => {
      const key = `${entry.projectId}-${entry.workType}`;
      if (!groupedEntries.has(key)) {
        groupedEntries.set(key, []);
      }
      groupedEntries.get(key)!.push(entry);
    });

    // Calculate costs for each group
    groupedEntries.forEach((entries, key) => {
      const [projectId, workType] = key.split('-');
      const projectIdNum = parseInt(projectId, 10);
      const workTypeEnum = workType as WorkType;
      
      const totalGroupHours = entries.reduce((sum, entry) => sum + entry.hours, 0);
      const rate = this.getRate(projectIdNum, workTypeEnum, userId);
      const cost = totalGroupHours * rate;

      breakdown.push({
        projectId: projectIdNum,
        projectTitle: entries[0].projectTitle,
        workType: workTypeEnum,
        hours: totalGroupHours,
        rate: rate,
        cost: cost
      });

      // Add to totals
      totalHours += totalGroupHours;
      totalCost += cost;

      // Add to work type totals
      switch (workTypeEnum) {
        case WorkType.Office:
          officeHours += totalGroupHours;
          officeCost += cost;
          break;
        case WorkType.Onsite:
          onsiteHours += totalGroupHours;
          onsiteCost += cost;
          break;
        case WorkType.WFH:
          wfhHours += totalGroupHours;
          wfhCost += cost;
          break;
      }
    });

    return {
      totalHours,
      totalCost,
      officeHours,
      officeCost,
      onsiteHours,
      onsiteCost,
      wfhHours,
      wfhCost,
      breakdown
    };
  }

  public getRate(projectId: number, workType: WorkType, userId?: string): number {
    // Priority: User-specific rate > Project-specific rate > Default rate
    
    let rates = this.rateConfig.defaultRates;
    
    // Check for project-specific rates
    if (this.rateConfig.projectRates.has(projectId)) {
      rates = this.rateConfig.projectRates.get(projectId)!;
    }
    
    // Check for user-specific rates (highest priority)
    if (userId && this.rateConfig.userRates.has(userId)) {
      rates = this.rateConfig.userRates.get(userId)!;
    }

    // Return appropriate rate based on work type
    switch (workType) {
      case WorkType.Onsite:
        return rates.onsiteRate;
      case WorkType.Office:
      case WorkType.WFH:
      default:
        return rates.hourlyRate;
    }
  }

  public updateProjectRates(projectId: number, hourlyRate: number, onsiteRate: number): void {
    this.rateConfig.projectRates.set(projectId, { hourlyRate, onsiteRate });
  }

  public updateUserRates(userId: string, hourlyRate: number, onsiteRate: number): void {
    this.rateConfig.userRates.set(userId, { hourlyRate, onsiteRate });
  }

  public updateDefaultRates(hourlyRate: number, onsiteRate: number): void {
    this.rateConfig.defaultRates = { hourlyRate, onsiteRate };
  }

  public exportCostReport(calculation: ICostCalculation, format: 'csv' | 'json' = 'csv'): string {
    if (format === 'json') {
      return JSON.stringify(calculation, null, 2);
    }

    // CSV format
    const headers = ['Project', 'Work Type', 'Hours', 'Rate', 'Cost'];
    const rows = calculation.breakdown.map(item => [
      item.projectTitle,
      item.workType,
      item.hours.toString(),
      item.rate.toString(),
      item.cost.toFixed(2)
    ]);

    // Add summary rows
    rows.push(['', '', '', '', '']);
    rows.push(['SUMMARY', '', '', '', '']);
    rows.push(['Office Work', '', calculation.officeHours.toString(), '', calculation.officeCost.toFixed(2)]);
    rows.push(['Onsite Work', '', calculation.onsiteHours.toString(), '', calculation.onsiteCost.toFixed(2)]);
    rows.push(['WFH Work', '', calculation.wfhHours.toString(), '', calculation.wfhCost.toFixed(2)]);
    rows.push(['TOTAL', '', calculation.totalHours.toString(), '', calculation.totalCost.toFixed(2)]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    return csvContent;
  }

  public calculateProjectProfitability(
    projectId: number,
    timeEntries: ITimeEntry[],
    projectBudget?: number
  ): {
    totalCost: number;
    totalHours: number;
    averageRate: number;
    budget?: number;
    remainingBudget?: number;
    profitMargin?: number;
  } {
    const projectEntries = timeEntries.filter(entry => entry.projectId === projectId);
    const calculation = this.calculateCosts(projectEntries);

    const result = {
      totalCost: calculation.totalCost,
      totalHours: calculation.totalHours,
      averageRate: calculation.totalHours > 0 ? calculation.totalCost / calculation.totalHours : 0,
      budget: projectBudget,
      remainingBudget: projectBudget ? projectBudget - calculation.totalCost : undefined,
      profitMargin: projectBudget ? ((projectBudget - calculation.totalCost) / projectBudget) * 100 : undefined
    };

    return result;
  }

  public generateRateRecommendations(
    timeEntries: ITimeEntry[],
    targetProfitMargin: number = 20
  ): {
    currentMargin: number;
    recommendedOfficeRate: number;
    recommendedOnsiteRate: number;
    analysis: string;
  } {
    const calculation = this.calculateCosts(timeEntries);
    const currentRevenue = calculation.totalCost;
    const targetRevenue = currentRevenue * (1 + targetProfitMargin / 100);
    
    // Calculate ratios for future use
    // const officeHoursRatio = calculation.officeHours / calculation.totalHours;
    // const onsiteHoursRatio = calculation.onsiteHours / calculation.totalHours;
    
    // Simple recommendation: increase rates proportionally
    const currentOfficeRate = this.rateConfig.defaultRates.hourlyRate;
    const currentOnsiteRate = this.rateConfig.defaultRates.onsiteRate;
    
    const multiplier = targetRevenue / currentRevenue;
    
    return {
      currentMargin: 0, // Would need cost data to calculate actual margin
      recommendedOfficeRate: Math.round(currentOfficeRate * multiplier),
      recommendedOnsiteRate: Math.round(currentOnsiteRate * multiplier),
      analysis: `To achieve ${targetProfitMargin}% profit margin, consider increasing rates by ${((multiplier - 1) * 100).toFixed(1)}%`
    };
  }
}
