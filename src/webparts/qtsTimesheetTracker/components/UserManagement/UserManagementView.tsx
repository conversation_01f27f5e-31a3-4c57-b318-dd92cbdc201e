import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  MessageBar,
  MessageBarType,
  PrimaryButton,
  DefaultButton,
  CommandBar,
  ICommandBarItemProps,
  Text,
  DetailsList,
  IColumn,
  DetailsListLayoutMode,
  SelectionMode,
  Stack,
  Dropdown,
  IDropdownOption,
  TextField,
  Dialog,
  DialogFooter,
  Spinner,
  SpinnerSize,
  IconButton,
  TooltipHost,
  Panel,
  PanelType,
  Checkbox
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, UserRole } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import styles from '../QtsTimesheetTracker.module.scss';

interface IUserManagementViewProps {
  context: WebPartContext;
  currentUser: IUser;
  sharePointService: SharePointService;
}

const UserManagementView: React.FC<IUserManagementViewProps> = ({
  context,
  currentUser,
  sharePointService
}) => {
  const [users, setUsers] = useState<IUser[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);
  const [selectedUser, setSelectedUser] = useState<IUser | null>(null);
  const [showUserPanel, setShowUserPanel] = useState<boolean>(false);
  const [editingUser, setEditingUser] = useState<IUser | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);

  useEffect(() => {
    loadUsers().catch(console.error);
  }, []);

  const loadUsers = async (): Promise<void> => {
    try {
      setLoading(true);
      const allUsers = await sharePointService.getUsers();
      setUsers(allUsers);
    } catch (error) {
      setMessage({
        text: 'Failed to load users. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const openUserPanel = (user?: IUser): void => {
    setEditingUser(user || {
      id: '',
      displayName: '',
      email: '',
      role: UserRole.User,
      hourlyRate: 0,
      onsiteRate: 0,
      maxDailyHours: 8,
      allowWeekends: false,
      manager: '',
      department: '',
      isActive: true
    });
    setShowUserPanel(true);
  };

  const closeUserPanel = (): void => {
    setShowUserPanel(false);
    setEditingUser(null);
  };

  const saveUser = async (): Promise<void> => {
    if (!editingUser || !editingUser.displayName.trim() || !editingUser.email.trim()) {
      setMessage({
        text: 'User name and email are required.',
        type: MessageBarType.error
      });
      return;
    }

    try {
      setLoading(true);
      let success = false;
      
      if (editingUser.id) {
        // Update existing user
        success = await sharePointService.updateUser(editingUser);
      } else {
        // Create new user
        const createdUser = await sharePointService.createUser(editingUser);
        success = !!createdUser;
      }

      if (success) {
        await loadUsers();
        closeUserPanel();
        setMessage({
          text: `User ${editingUser.id ? 'updated' : 'created'} successfully!`,
          type: MessageBarType.success
        });
      } else {
        setMessage({
          text: `Failed to ${editingUser.id ? 'update' : 'create'} user. Please try again.`,
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error saving user. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error saving user:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteUser = async (user: IUser): Promise<void> => {
    if (!user.id) return;

    try {
      setLoading(true);
      const success = await sharePointService.deleteUser(user.id);
      
      if (success) {
        await loadUsers();
        setMessage({
          text: 'User deleted successfully!',
          type: MessageBarType.success
        });
      } else {
        setMessage({
          text: 'Failed to delete user. Please try again.',
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error deleting user. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error deleting user:', error);
    } finally {
      setLoading(false);
      setShowDeleteDialog(false);
      setSelectedUser(null);
    }
  };

  const canManageUsers = (): boolean => {
    return currentUser.role === UserRole.Administrator ||
           currentUser.role === UserRole.ProgramManager;
  };

  const canEditUser = (user: IUser): boolean => {
    if (!canManageUsers()) return false;
    
    // Administrators can edit anyone
    if (currentUser.role === UserRole.Administrator) return true;
    
    // Program Managers cannot edit other administrators
    if (currentUser.role === UserRole.ProgramManager && user.role === UserRole.Administrator) {
      return false;
    }
    
    return true;
  };

  const getRoleColor = (role: UserRole): string => {
    switch (role) {
      case UserRole.Administrator: return '#a80000';
      case UserRole.ProgramManager: return '#d83b01';
      case UserRole.ProjectManager: return '#0078d4';
      case UserRole.Coordinator: return '#107c10';
      case UserRole.ProjectObserver: return '#605e5c';
      case UserRole.User: return '#323130';
      default: return '#605e5c';
    }
  };

  const commandBarItems: ICommandBarItemProps[] = [
    {
      key: 'newUser',
      text: 'New User',
      iconProps: { iconName: 'AddFriend' },
      onClick: () => openUserPanel(),
      disabled: !canManageUsers()
    },
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => { loadUsers().catch(console.error); }
    }
  ];

  const roleOptions: IDropdownOption[] = [
    { key: UserRole.Administrator, text: 'Administrator' },
    { key: UserRole.ProgramManager, text: 'Program Manager' },
    { key: UserRole.ProjectManager, text: 'Project Manager' },
    { key: UserRole.Coordinator, text: 'Coordinator' },
    { key: UserRole.ProjectObserver, text: 'Project Observer' },
    { key: UserRole.User, text: 'User' }
  ];

  const userColumns: IColumn[] = [
    {
      key: 'displayName',
      name: 'Name',
      fieldName: 'displayName',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true,
      onRender: (item: IUser) => (
        <div>
          <Text variant="medium" style={{ fontWeight: 'bold' }}>
            {item.displayName}
          </Text>
          <Text variant="small" block style={{ color: '#605e5c' }}>
            {item.email}
          </Text>
        </div>
      )
    },
    {
      key: 'role',
      name: 'Role',
      fieldName: 'role',
      minWidth: 120,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: IUser) => (
        <Text variant="small" style={{ color: getRoleColor(item.role), fontWeight: 'bold' }}>
          {item.role}
        </Text>
      )
    },
    {
      key: 'department',
      name: 'Department',
      fieldName: 'department',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: IUser) => (
        <Text variant="small">{item.department || '-'}</Text>
      )
    },
    {
      key: 'rates',
      name: 'Rates',
      minWidth: 120,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: IUser) => (
        <div>
          {item.hourlyRate && (
            <Text variant="small" block>
              Office: ${item.hourlyRate}/hr
            </Text>
          )}
          {item.onsiteRate && (
            <Text variant="small" block>
              Onsite: ${item.onsiteRate}/hr
            </Text>
          )}
        </div>
      )
    },
    {
      key: 'status',
      name: 'Status',
      minWidth: 80,
      maxWidth: 100,
      isResizable: true,
      onRender: (item: IUser) => (
        <Text variant="small" style={{ 
          color: item.isActive ? '#107c10' : '#a80000',
          fontWeight: 'bold'
        }}>
          {item.isActive ? 'Active' : 'Inactive'}
        </Text>
      )
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 100,
      maxWidth: 100,
      isResizable: false,
      onRender: (item: IUser) => (
        <Stack horizontal tokens={{ childrenGap: 5 }}>
          {canEditUser(item) && (
            <>
              <TooltipHost content="Edit User">
                <IconButton
                  iconProps={{ iconName: 'Edit' }}
                  onClick={() => openUserPanel(item)}
                />
              </TooltipHost>
              <TooltipHost content="Delete User">
                <IconButton
                  iconProps={{ iconName: 'Delete' }}
                  onClick={() => {
                    setSelectedUser(item);
                    setShowDeleteDialog(true);
                  }}
                  styles={{ root: { color: '#a80000' } }}
                  disabled={item.id === currentUser.id} // Cannot delete self
                />
              </TooltipHost>
            </>
          )}
        </Stack>
      )
    }
  ];

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>User Management</div>

      <CommandBar
        items={commandBarItems}
        className={styles.commandBar}
      />

      {loading && (
        <div className={styles.loadingContainer}>
          <Spinner size={SpinnerSize.large} label="Loading users..." />
        </div>
      )}

      <DetailsList
        items={users}
        columns={userColumns}
        layoutMode={DetailsListLayoutMode.justified}
        selectionMode={SelectionMode.none}
        isHeaderVisible={true}
        className={styles.detailsList}
      />

      {users.length === 0 && !loading && (
        <div className={styles.emptyState}>
          <div className={styles.emptyTitle}>No Users Found</div>
          <div className={styles.emptyDescription}>
            {canManageUsers() ? 
              'Get started by adding your first user to the system.' :
              'No users are currently configured in the system.'
            }
          </div>
          {canManageUsers() && (
            <PrimaryButton 
              text="Add User" 
              iconProps={{ iconName: 'AddFriend' }}
              onClick={() => openUserPanel()}
            />
          )}
        </div>
      )}

      {/* User Edit Panel */}
      <Panel
        isOpen={showUserPanel}
        onDismiss={closeUserPanel}
        type={PanelType.medium}
        headerText={editingUser?.id ? 'Edit User' : 'New User'}
        closeButtonAriaLabel="Close"
      >
        {editingUser && (
          <Stack tokens={{ childrenGap: 20 }}>
            <TextField
              label="Display Name"
              value={editingUser.displayName}
              onChange={(_, value) => setEditingUser({
                ...editingUser,
                displayName: value || ''
              })}
              required
            />
            
            <TextField
              label="Email"
              value={editingUser.email}
              onChange={(_, value) => setEditingUser({
                ...editingUser,
                email: value || ''
              })}
              type="email"
              required
            />

            <Dropdown
              label="Role"
              options={roleOptions}
              selectedKey={editingUser.role}
              onChange={(_, option) => setEditingUser({
                ...editingUser,
                role: option?.key as UserRole
              })}
              required
            />

            <TextField
              label="Department"
              value={editingUser.department}
              onChange={(_, value) => setEditingUser({
                ...editingUser,
                department: value || ''
              })}
            />

            <Stack horizontal tokens={{ childrenGap: 15 }}>
              <Stack.Item grow>
                <TextField
                  label="Hourly Rate ($)"
                  value={editingUser.hourlyRate?.toString() || ''}
                  onChange={(_, value) => setEditingUser({
                    ...editingUser,
                    hourlyRate: parseFloat(value || '0') || 0
                  })}
                  type="number"
                  min="0"
                  step="0.01"
                />
              </Stack.Item>
              <Stack.Item grow>
                <TextField
                  label="Onsite Rate ($)"
                  value={editingUser.onsiteRate?.toString() || ''}
                  onChange={(_, value) => setEditingUser({
                    ...editingUser,
                    onsiteRate: parseFloat(value || '0') || 0
                  })}
                  type="number"
                  min="0"
                  step="0.01"
                />
              </Stack.Item>
            </Stack>

            <TextField
              label="Max Daily Hours"
              value={editingUser.maxDailyHours?.toString() || '8'}
              onChange={(_, value) => setEditingUser({
                ...editingUser,
                maxDailyHours: parseInt(value || '8', 10) || 8
              })}
              type="number"
              min="1"
              max="24"
            />

            <Checkbox
              label="Allow Weekend Time Entry"
              checked={editingUser.allowWeekends}
              onChange={(_, checked) => setEditingUser({
                ...editingUser,
                allowWeekends: !!checked
              })}
            />

            <Checkbox
              label="Active User"
              checked={editingUser.isActive}
              onChange={(_, checked) => setEditingUser({
                ...editingUser,
                isActive: !!checked
              })}
            />

            <Stack horizontal tokens={{ childrenGap: 10 }} horizontalAlign="end">
              <PrimaryButton
                text="Save"
                onClick={() => saveUser().catch(console.error)}
                disabled={loading || !editingUser.displayName.trim() || !editingUser.email.trim()}
              />
              <DefaultButton
                text="Cancel"
                onClick={closeUserPanel}
              />
            </Stack>
          </Stack>
        )}
      </Panel>

      {/* Delete Confirmation Dialog */}
      <Dialog
        hidden={!showDeleteDialog}
        onDismiss={() => setShowDeleteDialog(false)}
        dialogContentProps={{
          title: 'Delete User',
          subText: selectedUser ? 
            `Are you sure you want to delete ${selectedUser.displayName}? This action cannot be undone.` :
            ''
        }}
        modalProps={{ isBlocking: true }}
      >
        <DialogFooter>
          <PrimaryButton 
            onClick={() => selectedUser && deleteUser(selectedUser).catch(console.error)} 
            text="Delete" 
            disabled={loading}
            styles={{ root: { backgroundColor: '#a80000' } }}
          />
          <DefaultButton onClick={() => setShowDeleteDialog(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default UserManagementView;
