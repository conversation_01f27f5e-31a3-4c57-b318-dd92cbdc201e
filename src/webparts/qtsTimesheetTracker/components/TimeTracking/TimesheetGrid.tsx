import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  DetailsList,
  IColumn,
  DetailsListLayoutMode,
  SelectionMode,
  TextField,
  Dropdown,
  IDropdownOption,
  PrimaryButton,
  DefaultButton,
  IconButton,
  MessageBar,
  MessageBarType,
  Dialog,
  DialogFooter,
  DialogContent,
  Text,
  Stack,
  TooltipHost
} from '@fluentui/react';
import { ITimeEntry, IProject, WorkType } from '../../models';
import { DateUtils } from '../../utils/DateUtils';
import styles from '../QtsTimesheetTracker.module.scss';

interface ITimesheetGridProps {
  weekStartDate: Date;
  timeEntries: ITimeEntry[];
  projects: IProject[];
  onTimeEntriesChange: (entries: ITimeEntry[]) => void;
  isReadOnly?: boolean;
  maxDailyHours?: number;
  allowWeekends?: boolean;
}

interface ITimeEntryRow {
  id: string;
  projectId: number;
  projectTitle: string;
  activityTitle?: string;
  monday: number;
  tuesday: number;
  wednesday: number;
  thursday: number;
  friday: number;
  saturday: number;
  sunday: number;
  total: number;
  workType: WorkType;
  description?: string;
}

const TimesheetGrid: React.FC<ITimesheetGridProps> = ({
  weekStartDate,
  timeEntries,
  projects,
  onTimeEntriesChange,
  isReadOnly = false,
  maxDailyHours = 24,
  allowWeekends = true
}) => {
  const [gridData, setGridData] = useState<ITimeEntryRow[]>([]);
  const [showAddDialog, setShowAddDialog] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<number | undefined>();
  const [selectedWorkType, setSelectedWorkType] = useState<WorkType>(WorkType.Office);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);

  const weekDates = DateUtils.getWeekDates(weekStartDate);
  const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  useEffect(() => {
    convertTimeEntriesToGridData();
  }, [timeEntries, projects]);

  const convertTimeEntriesToGridData = (): void => {
    const groupedEntries = new Map<string, ITimeEntry[]>();
    
    // Group time entries by project and work type
    timeEntries.forEach(entry => {
      const key = `${entry.projectId}-${entry.workType}`;
      if (!groupedEntries.has(key)) {
        groupedEntries.set(key, []);
      }
      groupedEntries.get(key)!.push(entry);
    });

    const rows: ITimeEntryRow[] = [];
    groupedEntries.forEach((entries, key) => {
      const [projectId, workType] = key.split('-');
      const project = projects.find(p => p.id === parseInt(projectId, 10));
      
      if (!project) return;

      const row: ITimeEntryRow = {
        id: key,
        projectId: parseInt(projectId, 10),
        projectTitle: project.title,
        workType: workType as WorkType,
        monday: 0,
        tuesday: 0,
        wednesday: 0,
        thursday: 0,
        friday: 0,
        saturday: 0,
        sunday: 0,
        total: 0,
        description: entries[0]?.description || ''
      };

      // Fill in the hours for each day
      entries.forEach(entry => {
        const dayIndex = entry.date.getDay();
        const adjustedIndex = dayIndex === 0 ? 6 : dayIndex - 1; // Convert Sunday=0 to index 6
        
        switch (adjustedIndex) {
          case 0: row.monday = entry.hours; break;
          case 1: row.tuesday = entry.hours; break;
          case 2: row.wednesday = entry.hours; break;
          case 3: row.thursday = entry.hours; break;
          case 4: row.friday = entry.hours; break;
          case 5: row.saturday = entry.hours; break;
          case 6: row.sunday = entry.hours; break;
        }
      });

      row.total = row.monday + row.tuesday + row.wednesday + row.thursday + row.friday + row.saturday + row.sunday;
      rows.push(row);
    });

    setGridData(rows);
  };

  const convertGridDataToTimeEntries = (rows: ITimeEntryRow[]): ITimeEntry[] => {
    const entries: ITimeEntry[] = [];
    
    rows.forEach(row => {
      const dailyHours = [
        { day: 0, hours: row.monday },
        { day: 1, hours: row.tuesday },
        { day: 2, hours: row.wednesday },
        { day: 3, hours: row.thursday },
        { day: 4, hours: row.friday },
        { day: 5, hours: row.saturday },
        { day: 6, hours: row.sunday }
      ];

      dailyHours.forEach(({ day, hours }) => {
        if (hours > 0) {
          const entryDate = new Date(weekDates[day].getTime());
          entries.push({
            projectId: row.projectId,
            projectTitle: row.projectTitle,
            date: entryDate,
            hours: hours,
            workType: row.workType,
            description: row.description
          });
        }
      });
    });

    return entries;
  };

  const updateRowHours = (rowId: string, day: string, hours: number): void => {
    if (isReadOnly) return;

    // Validate hours
    if (hours < 0 || hours > maxDailyHours) {
      setMessage({
        text: `Hours must be between 0 and ${maxDailyHours}`,
        type: MessageBarType.error
      });
      return;
    }

    const updatedRows = gridData.map(row => {
      if (row.id === rowId) {
        const updatedRow = { ...row, [day]: hours };
        updatedRow.total = updatedRow.monday + updatedRow.tuesday + updatedRow.wednesday + 
                          updatedRow.thursday + updatedRow.friday + updatedRow.saturday + updatedRow.sunday;
        return updatedRow;
      }
      return row;
    });

    setGridData(updatedRows);
    
    // Convert back to time entries and notify parent
    const newTimeEntries = convertGridDataToTimeEntries(updatedRows);
    onTimeEntriesChange(newTimeEntries);
  };

  const addNewRow = (): void => {
    if (!selectedProject) {
      setMessage({
        text: 'Please select a project',
        type: MessageBarType.error
      });
      return;
    }

    const project = projects.find(p => p.id === selectedProject);
    if (!project) return;

    const newRowId = `${selectedProject}-${selectedWorkType}`;
    
    // Check if row already exists
    if (gridData.some(row => row.id === newRowId)) {
      setMessage({
        text: 'A row for this project and work type already exists',
        type: MessageBarType.warning
      });
      return;
    }

    const newRow: ITimeEntryRow = {
      id: newRowId,
      projectId: selectedProject,
      projectTitle: project.title,
      workType: selectedWorkType,
      monday: 0,
      tuesday: 0,
      wednesday: 0,
      thursday: 0,
      friday: 0,
      saturday: 0,
      sunday: 0,
      total: 0
    };

    setGridData([...gridData, newRow]);
    setShowAddDialog(false);
    setSelectedProject(undefined);
    setSelectedWorkType(WorkType.Office);
  };

  const removeRow = (rowId: string): void => {
    if (isReadOnly) return;
    
    const updatedRows = gridData.filter(row => row.id !== rowId);
    setGridData(updatedRows);
    
    const newTimeEntries = convertGridDataToTimeEntries(updatedRows);
    onTimeEntriesChange(newTimeEntries);
  };

  const renderHoursCell = (item: ITimeEntryRow, day: string, dayIndex: number): JSX.Element => {
    const isWeekendDay = dayIndex >= 5; // Saturday and Sunday
    const isDisabled = isReadOnly || (!allowWeekends && isWeekendDay);
    const hours = item[day as keyof ITimeEntryRow] as number;

    return (
      <TextField
        value={hours > 0 ? hours.toString() : ''}
        onChange={(_, newValue) => {
          const numValue = parseFloat(newValue || '0') || 0;
          updateRowHours(item.id, day, numValue);
        }}
        disabled={isDisabled}
        styles={{
          root: { width: '60px' },
          field: {
            textAlign: 'center',
            backgroundColor: isWeekendDay && !allowWeekends ? '#f3f2f1' : undefined
          }
        }}
        type="number"
        min="0"
        max={maxDailyHours.toString()}
        step="0.25"
      />
    );
  };

  const columns: IColumn[] = [
    {
      key: 'project',
      name: 'Project',
      fieldName: 'projectTitle',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true,
      onRender: (item: ITimeEntryRow) => (
        <div>
          <Text variant="medium" block>{item.projectTitle}</Text>
          <Text variant="small" style={{ color: '#605e5c' }}>
            {item.workType}
          </Text>
        </div>
      )
    },
    ...dayNames.map((dayName, index) => ({
      key: dayName.toLowerCase(),
      name: dayName.substring(0, 3),
      minWidth: 70,
      maxWidth: 70,
      isResizable: false,
      onRender: (item: ITimeEntryRow) => renderHoursCell(item, dayName.toLowerCase(), index)
    })),
    {
      key: 'total',
      name: 'Total',
      minWidth: 60,
      maxWidth: 60,
      isResizable: false,
      onRender: (item: ITimeEntryRow) => (
        <Text variant="medium" style={{ fontWeight: 'bold' }}>
          {item.total.toFixed(2)}
        </Text>
      )
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 50,
      maxWidth: 50,
      isResizable: false,
      onRender: (item: ITimeEntryRow) => (
        !isReadOnly && (
          <TooltipHost content="Remove row">
            <IconButton
              iconProps={{ iconName: 'Delete' }}
              onClick={() => removeRow(item.id)}
              styles={{ root: { color: '#a80000' } }}
            />
          </TooltipHost>
        )
      )
    }
  ];

  const projectOptions: IDropdownOption[] = projects.map(project => ({
    key: project.id!,
    text: project.title
  }));

  const workTypeOptions: IDropdownOption[] = [
    { key: WorkType.Office, text: 'Office' },
    { key: WorkType.Onsite, text: 'Onsite' },
    { key: WorkType.WFH, text: 'Work From Home' }
  ];

  return (
    <div className={styles.timesheetGrid}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.gridHeader}>
        <Text variant="large" block>Time Entries</Text>
        {!isReadOnly && (
          <PrimaryButton
            text="Add Row"
            iconProps={{ iconName: 'Add' }}
            onClick={() => setShowAddDialog(true)}
          />
        )}
      </div>

      <DetailsList
        items={gridData}
        columns={columns}
        layoutMode={DetailsListLayoutMode.justified}
        selectionMode={SelectionMode.none}
        isHeaderVisible={true}
        className={styles.detailsList}
      />

      <Dialog
        hidden={!showAddDialog}
        onDismiss={() => setShowAddDialog(false)}
        dialogContentProps={{
          title: 'Add Time Entry Row',
          subText: 'Select a project and work type to add a new time entry row.'
        }}
        modalProps={{ isBlocking: true }}
      >
        <DialogContent>
          <Stack tokens={{ childrenGap: 15 }}>
            <Dropdown
              label="Project"
              options={projectOptions}
              selectedKey={selectedProject}
              onChange={(_, option) => setSelectedProject(option?.key as number)}
              placeholder="Select a project"
              required
            />
            <Dropdown
              label="Work Type"
              options={workTypeOptions}
              selectedKey={selectedWorkType}
              onChange={(_, option) => setSelectedWorkType(option?.key as WorkType)}
              required
            />
          </Stack>
        </DialogContent>
        <DialogFooter>
          <PrimaryButton onClick={addNewRow} text="Add" />
          <DefaultButton onClick={() => setShowAddDialog(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default TimesheetGrid;
