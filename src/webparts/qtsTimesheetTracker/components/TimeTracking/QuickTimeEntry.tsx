import * as React from 'react';
import { useState } from 'react';
import {
  <PERSON>Butt<PERSON>,
  <PERSON><PERSON>ult<PERSON>utton,
  TextField,
  Dropdown,
  IDropdownOption,
  DatePicker,
  Stack,
  Text,
  MessageBar,
  MessageBarType,
  Panel,
  PanelType,
  TimePicker,
  ITimeRange,
  Checkbox
} from '@fluentui/react';
import { IProject, WorkType, ITimeEntry } from '../../models';
import styles from '../QtsTimesheetTracker.module.scss';

interface IQuickTimeEntryProps {
  projects: IProject[];
  onTimeEntryComplete: (entry: ITimeEntry) => void;
  isVisible: boolean;
  onDismiss: () => void;
  defaultDate?: Date;
  defaultProject?: number;
}

const QuickTimeEntry: React.FC<IQuickTimeEntryProps> = ({
  projects,
  onTimeEntryComplete,
  isVisible,
  onDismiss,
  defaultDate,
  defaultProject
}) => {
  const [selectedProject, setSelectedProject] = useState<number | undefined>(defaultProject);
  const [selectedWorkType, setSelectedWorkType] = useState<WorkType>(WorkType.Office);
  const [selectedDate, setSelectedDate] = useState<Date>(defaultDate || new Date());
  const [hours, setHours] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [useTimeRange, setUseTimeRange] = useState<boolean>(false);
  const [startTime, setStartTime] = useState<Date | undefined>();
  const [endTime, setEndTime] = useState<Date | undefined>();
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);

  const resetForm = (): void => {
    setSelectedProject(defaultProject);
    setSelectedWorkType(WorkType.Office);
    setSelectedDate(defaultDate || new Date());
    setHours('');
    setDescription('');
    setUseTimeRange(false);
    setStartTime(undefined);
    setEndTime(undefined);
    setMessage(null);
  };

  const validateForm = (): boolean => {
    if (!selectedProject) {
      setMessage({
        text: 'Please select a project',
        type: MessageBarType.error
      });
      return false;
    }

    if (useTimeRange) {
      if (!startTime || !endTime) {
        setMessage({
          text: 'Please select both start and end times',
          type: MessageBarType.error
        });
        return false;
      }

      if (startTime >= endTime) {
        setMessage({
          text: 'End time must be after start time',
          type: MessageBarType.error
        });
        return false;
      }
    } else {
      const hoursValue = parseFloat(hours);
      if (!hours || isNaN(hoursValue) || hoursValue <= 0) {
        setMessage({
          text: 'Please enter valid hours (greater than 0)',
          type: MessageBarType.error
        });
        return false;
      }

      if (hoursValue > 24) {
        setMessage({
          text: 'Hours cannot exceed 24 per day',
          type: MessageBarType.error
        });
        return false;
      }
    }

    return true;
  };

  const calculateHoursFromTimeRange = (): number => {
    if (!startTime || !endTime) return 0;
    
    const diffMs = endTime.getTime() - startTime.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    return Math.round(diffHours * 100) / 100; // Round to 2 decimal places
  };

  const handleSave = (): void => {
    if (!validateForm()) return;

    const project = projects.find(p => p.id === selectedProject);
    if (!project) return;

    const finalHours = useTimeRange ? calculateHoursFromTimeRange() : parseFloat(hours);

    const timeEntry: ITimeEntry = {
      projectId: selectedProject!,
      projectTitle: project.title,
      date: selectedDate,
      hours: finalHours,
      workType: selectedWorkType,
      description: description.trim() || undefined,
      startTime: useTimeRange ? startTime : undefined,
      endTime: useTimeRange ? endTime : undefined
    };

    onTimeEntryComplete(timeEntry);
    resetForm();
    onDismiss();

    setMessage({
      text: `Time entry saved: ${finalHours} hours for ${project.title}`,
      type: MessageBarType.success
    });
  };

  const handleCancel = (): void => {
    resetForm();
    onDismiss();
  };

  const projectOptions: IDropdownOption[] = projects.map(project => ({
    key: project.id!,
    text: project.title
  }));

  const workTypeOptions: IDropdownOption[] = [
    { key: WorkType.Office, text: 'Office' },
    { key: WorkType.Onsite, text: 'Onsite' },
    { key: WorkType.WFH, text: 'Work From Home' }
  ];

  const renderTimeInput = (): JSX.Element => {
    if (useTimeRange) {
      return (
        <Stack tokens={{ childrenGap: 10 }}>
          <Stack horizontal tokens={{ childrenGap: 15 }}>
            <Stack.Item grow>
              <Text variant="medium" block style={{ marginBottom: '5px' }}>
                Start Time
              </Text>
              <TimePicker
                value={startTime}
                onChange={(_, time) => setStartTime(time)}
                allowFreeform
                timeRange={{
                  start: 0,
                  end: 24 * 60 - 1
                } as ITimeRange}
                increments={15}
              />
            </Stack.Item>
            <Stack.Item grow>
              <Text variant="medium" block style={{ marginBottom: '5px' }}>
                End Time
              </Text>
              <TimePicker
                value={endTime}
                onChange={(_, time) => setEndTime(time)}
                allowFreeform
                timeRange={{
                  start: 0,
                  end: 24 * 60 - 1
                } as ITimeRange}
                increments={15}
              />
            </Stack.Item>
          </Stack>
          {startTime && endTime && (
            <Text variant="small" style={{ color: '#605e5c' }}>
              Duration: {calculateHoursFromTimeRange()} hours
            </Text>
          )}
        </Stack>
      );
    }

    return (
      <TextField
        label="Hours"
        value={hours}
        onChange={(_, newValue) => setHours(newValue || '')}
        type="number"
        min="0"
        max="24"
        step="0.25"
        placeholder="e.g., 8.5"
        required
      />
    );
  };

  return (
    <Panel
      isOpen={isVisible}
      onDismiss={onDismiss}
      type={PanelType.medium}
      headerText="Quick Time Entry"
      closeButtonAriaLabel="Close"
    >
      <div className={styles.quickTimeEntryContainer}>
        {message && (
          <MessageBar
            messageBarType={message.type}
            onDismiss={() => setMessage(null)}
            dismissButtonAriaLabel="Close"
          >
            {message.text}
          </MessageBar>
        )}

        <Stack tokens={{ childrenGap: 20 }}>
          <Dropdown
            label="Project"
            options={projectOptions}
            selectedKey={selectedProject}
            onChange={(_, option) => setSelectedProject(option?.key as number)}
            placeholder="Select a project"
            required
          />

          <Dropdown
            label="Work Type"
            options={workTypeOptions}
            selectedKey={selectedWorkType}
            onChange={(_, option) => setSelectedWorkType(option?.key as WorkType)}
            required
          />

          <DatePicker
            label="Date"
            value={selectedDate}
            onSelectDate={(date) => date && setSelectedDate(date)}
            placeholder="Select date"
            ariaLabel="Select date"
            isRequired
          />

          <Checkbox
            label="Use time range instead of hours"
            checked={useTimeRange}
            onChange={(_, checked) => setUseTimeRange(!!checked)}
          />

          {renderTimeInput()}

          <TextField
            label="Description (Optional)"
            value={description}
            onChange={(_, newValue) => setDescription(newValue || '')}
            multiline
            rows={3}
            placeholder="Describe the work performed..."
          />

          <Stack horizontal tokens={{ childrenGap: 10 }} horizontalAlign="end">
            <PrimaryButton
              text="Save Entry"
              iconProps={{ iconName: 'Save' }}
              onClick={handleSave}
            />
            <DefaultButton
              text="Cancel"
              onClick={handleCancel}
            />
          </Stack>
        </Stack>

        <div className={styles.quickEntryInstructions}>
          <Text variant="small" style={{ color: '#605e5c' }}>
            <strong>Quick Entry Tips:</strong>
            <br />
            • Enter hours in decimal format (e.g., 1.5 for 1 hour 30 minutes)
            • Use time range for precise start/end time tracking
            • Description helps with detailed reporting
            • All entries are added to your current timesheet
          </Text>
        </div>
      </div>
    </Panel>
  );
};

export default QuickTimeEntry;
