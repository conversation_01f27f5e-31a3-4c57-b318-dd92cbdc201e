import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  MessageBar,
  MessageBarType,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Text
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject, ITimesheet, TimesheetStatus, ITimeEntry } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import { DateUtils } from '../../utils/DateUtils';
import TimesheetGrid from './TimesheetGrid';
import StopwatchTimer from './StopwatchTimer';
import QuickTimeEntry from './QuickTimeEntry';
import styles from '../QtsTimesheetTracker.module.scss';

interface ITimesheetViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
}

const TimesheetView: React.FC<ITimesheetViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService
}) => {
  const [currentWeek, setCurrentWeek] = useState<Date>(new Date());
  const [currentTimesheet, setCurrentTimesheet] = useState<ITimesheet | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);
  const [showStopwatch, setShowStopwatch] = useState<boolean>(false);
  const [showQuickEntry, setShowQuickEntry] = useState<boolean>(false);

  const weekStart = DateUtils.getWeekStart(currentWeek);
  const weekEnd = DateUtils.getWeekEnd(currentWeek);
  // const weekDates = DateUtils.getWeekDates(currentWeek);

  const loadTimesheetData = async (): Promise<void> => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const timesheets = await sharePointService.getTimesheets(currentUser.id, weekStart);
      
      if (timesheets.length > 0) {
        setCurrentTimesheet(timesheets[0]);
      } else {
        // Create a new timesheet for this week
        const newTimesheet: ITimesheet = {
          userId: currentUser.id,
          userName: currentUser.displayName,
          weekStartDate: weekStart,
          weekEndDate: weekEnd,
          timeEntries: [],
          totalHours: 0,
          status: TimesheetStatus.Draft
        };
        setCurrentTimesheet(newTimesheet);
      }
    } catch (error) {
      setMessage({
        text: 'Failed to load timesheet data. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error loading timesheet:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    void loadTimesheetData();
  }, [currentWeek, currentUser]);

  const navigateWeek = (direction: 'previous' | 'next'): void => {
    const newWeek = DateUtils.addWeeks(currentWeek, direction === 'next' ? 1 : -1);
    setCurrentWeek(newWeek);
  };

  const handleTimeEntriesChange = async (entries: ITimeEntry[]): Promise<void> => {
    if (!currentTimesheet) return;

    const updatedTimesheet: ITimesheet = {
      ...currentTimesheet,
      timeEntries: entries,
      totalHours: entries.reduce((total, entry) => total + entry.hours, 0),
      modified: new Date()
    };

    try {
      setLoading(true);
      let success = false;

      if (currentTimesheet.id) {
        success = await sharePointService.updateTimesheet(updatedTimesheet);
      } else {
        const createdTimesheet = await sharePointService.createTimesheet(updatedTimesheet);
        success = !!createdTimesheet;
        if (createdTimesheet) {
          updatedTimesheet.id = createdTimesheet.id;
        }
      }

      if (success) {
        setCurrentTimesheet(updatedTimesheet);
      } else {
        setMessage({
          text: 'Failed to save timesheet changes. Please try again.',
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error saving timesheet changes. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error saving timesheet:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTimeEntryComplete = (entry: ITimeEntry): void => {
    if (!currentTimesheet) return;

    const existingEntries = [...currentTimesheet.timeEntries];

    // Check if there's already an entry for this project, date, and work type
    const existingIndex = existingEntries.findIndex(e =>
      e.projectId === entry.projectId &&
      DateUtils.formatDate(e.date) === DateUtils.formatDate(entry.date) &&
      e.workType === entry.workType
    );

    if (existingIndex >= 0) {
      // Update existing entry
      existingEntries[existingIndex] = {
        ...existingEntries[existingIndex],
        hours: existingEntries[existingIndex].hours + entry.hours,
        description: entry.description || existingEntries[existingIndex].description,
        modified: new Date()
      };
    } else {
      // Add new entry
      existingEntries.push(entry);
    }

    handleTimeEntriesChange(existingEntries).catch(console.error);
  };

  const submitTimesheet = async (): Promise<void> => {
    if (!currentTimesheet) return;

    try {
      setLoading(true);
      const updatedTimesheet: ITimesheet = {
        ...currentTimesheet,
        status: TimesheetStatus.Submitted,
        submittedDate: new Date()
      };

      const success = await sharePointService.updateTimesheet(updatedTimesheet);
      if (success) {
        setCurrentTimesheet(updatedTimesheet);
        setMessage({
          text: 'Timesheet submitted successfully!',
          type: MessageBarType.success
        });
      } else {
        setMessage({
          text: 'Failed to submit timesheet. Please try again.',
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error submitting timesheet. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error submitting timesheet:', error);
    } finally {
      setLoading(false);
    }
  };

  const commandBarItems: ICommandBarItemProps[] = [
    {
      key: 'previousWeek',
      text: 'Previous Week',
      iconProps: { iconName: 'ChevronLeft' },
      onClick: () => navigateWeek('previous')
    },
    {
      key: 'nextWeek',
      text: 'Next Week',
      iconProps: { iconName: 'ChevronRight' },
      onClick: () => navigateWeek('next')
    },
    {
      key: 'currentWeek',
      text: 'Current Week',
      iconProps: { iconName: 'Calendar' },
      onClick: () => setCurrentWeek(new Date())
    }
  ];

  const farCommandBarItems: ICommandBarItemProps[] = [
    {
      key: 'quickEntry',
      text: 'Quick Entry',
      iconProps: { iconName: 'Add' },
      disabled: loading,
      onClick: () => setShowQuickEntry(true)
    },
    {
      key: 'stopwatch',
      text: 'Stopwatch',
      iconProps: { iconName: 'Timer' },
      disabled: loading,
      onClick: () => setShowStopwatch(true)
    },
    {
      key: 'submit',
      text: 'Submit Timesheet',
      iconProps: { iconName: 'Send' },
      disabled: !currentTimesheet || currentTimesheet.status !== TimesheetStatus.Draft || loading,
      onClick: (): void => {
        submitTimesheet().catch(console.error);
      }
    }
  ];

  const getTotalHours = (): number => {
    if (!currentTimesheet) return 0;
    return currentTimesheet.timeEntries.reduce((total, entry) => total + entry.hours, 0);
  };

  const getStatusColor = (status: TimesheetStatus): string => {
    switch (status) {
      case TimesheetStatus.Draft:
        return '#605e5c';
      case TimesheetStatus.Submitted:
        return '#d83b01';
      case TimesheetStatus.Approved:
        return '#107c10';
      case TimesheetStatus.Rejected:
        return '#a80000';
      default:
        return '#605e5c';
    }
  };

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>My Timesheet</div>

      <CommandBar
        items={commandBarItems}
        farItems={farCommandBarItems}
        className={styles.commandBar}
      />

      <div className={styles.timesheetHeader}>
        <Text variant="xLarge" block>
          Week of {DateUtils.formatWeekRange(weekStart, weekEnd)}
        </Text>
        {currentTimesheet && (
          <Text 
            variant="medium" 
            style={{ color: getStatusColor(currentTimesheet.status) }}
          >
            Status: {currentTimesheet.status}
          </Text>
        )}
      </div>

      {currentTimesheet && (
        <div className={styles.summaryCards}>
          <div className={styles.summaryCard}>
            <div className={styles.cardTitle}>Total Hours</div>
            <div className={styles.cardValue}>{getTotalHours().toFixed(2)}</div>
          </div>
          <div className={styles.summaryCard}>
            <div className={styles.cardTitle}>Status</div>
            <div className={styles.cardValue}>{currentTimesheet.status}</div>
          </div>
          <div className={styles.summaryCard}>
            <div className={styles.cardTitle}>Week</div>
            <div className={styles.cardValue}>
              {DateUtils.getWeekNumber(weekStart)}
            </div>
          </div>
        </div>
      )}

      {!currentTimesheet ? (
        <div className={styles.emptyState}>
          <div className={styles.emptyTitle}>No Timesheet Found</div>
          <div className={styles.emptyDescription}>
            No timesheet exists for this week. Create one to start tracking your time.
          </div>
          <PrimaryButton 
            text="Create Timesheet" 
            onClick={loadTimesheetData}
            disabled={loading}
          />
        </div>
      ) : (
        <TimesheetGrid
          weekStartDate={weekStart}
          timeEntries={currentTimesheet.timeEntries}
          projects={projects}
          onTimeEntriesChange={handleTimeEntriesChange}
          isReadOnly={currentTimesheet.status !== TimesheetStatus.Draft}
          maxDailyHours={24}
          allowWeekends={true}
        />
      )}

      {/* Time Tracking Panels */}
      <StopwatchTimer
        projects={projects}
        onTimeEntryComplete={handleTimeEntryComplete}
        isVisible={showStopwatch}
        onDismiss={() => setShowStopwatch(false)}
      />

      <QuickTimeEntry
        projects={projects}
        onTimeEntryComplete={handleTimeEntryComplete}
        isVisible={showQuickEntry}
        onDismiss={() => setShowQuickEntry(false)}
        defaultDate={new Date()}
      />
    </div>
  );
};

export default TimesheetView;