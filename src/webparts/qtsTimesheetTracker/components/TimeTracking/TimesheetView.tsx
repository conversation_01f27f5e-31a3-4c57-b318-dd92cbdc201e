import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  MessageBar,
  MessageBarType,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Text
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject, ITimesheet, TimesheetStatus } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import { DateUtils } from '../../utils/DateUtils';
import styles from '../QtsTimesheetTracker.module.scss';

interface ITimesheetViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
}

const TimesheetView: React.FC<ITimesheetViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService
}) => {
  const [currentWeek, setCurrentWeek] = useState<Date>(new Date());
  const [currentTimesheet, setCurrentTimesheet] = useState<ITimesheet | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);

  const weekStart = DateUtils.getWeekStart(currentWeek);
  const weekEnd = DateUtils.getWeekEnd(currentWeek);
  // const weekDates = DateUtils.getWeekDates(currentWeek);

  const loadTimesheetData = async (): Promise<void> => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const timesheets = await sharePointService.getTimesheets(currentUser.id, weekStart);
      
      if (timesheets.length > 0) {
        setCurrentTimesheet(timesheets[0]);
      } else {
        // Create a new timesheet for this week
        const newTimesheet: ITimesheet = {
          userId: currentUser.id,
          userName: currentUser.displayName,
          weekStartDate: weekStart,
          weekEndDate: weekEnd,
          timeEntries: [],
          totalHours: 0,
          status: TimesheetStatus.Draft
        };
        setCurrentTimesheet(newTimesheet);
      }
    } catch (error) {
      setMessage({
        text: 'Failed to load timesheet data. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error loading timesheet:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    void loadTimesheetData();
  }, [currentWeek, currentUser]);

  const navigateWeek = (direction: 'previous' | 'next'): void => {
    const newWeek = DateUtils.addWeeks(currentWeek, direction === 'next' ? 1 : -1);
    setCurrentWeek(newWeek);
  };

  const submitTimesheet = async (): Promise<void> => {
    if (!currentTimesheet) return;

    try {
      setLoading(true);
      const updatedTimesheet: ITimesheet = {
        ...currentTimesheet,
        status: TimesheetStatus.Submitted,
        submittedDate: new Date()
      };

      const success = await sharePointService.updateTimesheet(updatedTimesheet);
      if (success) {
        setCurrentTimesheet(updatedTimesheet);
        setMessage({
          text: 'Timesheet submitted successfully!',
          type: MessageBarType.success
        });
      } else {
        setMessage({
          text: 'Failed to submit timesheet. Please try again.',
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error submitting timesheet. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error submitting timesheet:', error);
    } finally {
      setLoading(false);
    }
  };

  const commandBarItems: ICommandBarItemProps[] = [
    {
      key: 'previousWeek',
      text: 'Previous Week',
      iconProps: { iconName: 'ChevronLeft' },
      onClick: () => navigateWeek('previous')
    },
    {
      key: 'nextWeek',
      text: 'Next Week',
      iconProps: { iconName: 'ChevronRight' },
      onClick: () => navigateWeek('next')
    },
    {
      key: 'currentWeek',
      text: 'Current Week',
      iconProps: { iconName: 'Calendar' },
      onClick: () => setCurrentWeek(new Date())
    }
  ];

  const farCommandBarItems: ICommandBarItemProps[] = [
    {
      key: 'submit',
      text: 'Submit Timesheet',
      iconProps: { iconName: 'Send' },
      disabled: !currentTimesheet || currentTimesheet.status !== TimesheetStatus.Draft || loading,
      onClick: (): void => {
        submitTimesheet().catch(console.error);
      }
    }
  ];

  const getTotalHours = (): number => {
    if (!currentTimesheet) return 0;
    return currentTimesheet.timeEntries.reduce((total, entry) => total + entry.hours, 0);
  };

  const getStatusColor = (status: TimesheetStatus): string => {
    switch (status) {
      case TimesheetStatus.Draft:
        return '#605e5c';
      case TimesheetStatus.Submitted:
        return '#d83b01';
      case TimesheetStatus.Approved:
        return '#107c10';
      case TimesheetStatus.Rejected:
        return '#a80000';
      default:
        return '#605e5c';
    }
  };

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>My Timesheet</div>

      <CommandBar
        items={commandBarItems}
        farItems={farCommandBarItems}
        className={styles.commandBar}
      />

      <div className={styles.timesheetHeader}>
        <Text variant="xLarge" block>
          Week of {DateUtils.formatWeekRange(weekStart, weekEnd)}
        </Text>
        {currentTimesheet && (
          <Text 
            variant="medium" 
            style={{ color: getStatusColor(currentTimesheet.status) }}
          >
            Status: {currentTimesheet.status}
          </Text>
        )}
      </div>

      {currentTimesheet && (
        <div className={styles.summaryCards}>
          <div className={styles.summaryCard}>
            <div className={styles.cardTitle}>Total Hours</div>
            <div className={styles.cardValue}>{getTotalHours().toFixed(2)}</div>
          </div>
          <div className={styles.summaryCard}>
            <div className={styles.cardTitle}>Status</div>
            <div className={styles.cardValue}>{currentTimesheet.status}</div>
          </div>
          <div className={styles.summaryCard}>
            <div className={styles.cardTitle}>Week</div>
            <div className={styles.cardValue}>
              {DateUtils.getWeekNumber(weekStart)}
            </div>
          </div>
        </div>
      )}

      {!currentTimesheet ? (
        <div className={styles.emptyState}>
          <div className={styles.emptyTitle}>No Timesheet Found</div>
          <div className={styles.emptyDescription}>
            No timesheet exists for this week. Create one to start tracking your time.
          </div>
          <PrimaryButton 
            text="Create Timesheet" 
            onClick={loadTimesheetData}
            disabled={loading}
          />
        </div>
      ) : (
        <div className={styles.timesheetGrid}>
          {/* Timesheet grid will be implemented here */}
          <div className={styles.emptyState}>
            <div className={styles.emptyTitle}>Timesheet Entry</div>
            <div className={styles.emptyDescription}>
              Time entry grid will be implemented in the next phase.
              Currently showing timesheet structure for week of {DateUtils.formatWeekRange(weekStart, weekEnd)}.
            </div>
            <div>
              <strong>Available Projects:</strong>
              <ul style={{ textAlign: 'left', marginTop: '10px' }}>
                {projects.map(project => (
                  <li key={project.id}>{project.title}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimesheetView;