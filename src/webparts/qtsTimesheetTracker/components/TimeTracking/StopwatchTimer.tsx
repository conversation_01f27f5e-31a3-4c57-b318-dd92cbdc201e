import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import {
  PrimaryButton,
  DefaultButton,
  Text,
  Stack,
  Dropdown,
  IDropdownOption,
  TextField,
  MessageBar,
  MessageBarType,
  Panel,
  PanelType
} from '@fluentui/react';
import { IProject, WorkType, ITimeEntry } from '../../models';
import styles from '../QtsTimesheetTracker.module.scss';

interface IStopwatchTimerProps {
  projects: IProject[];
  onTimeEntryComplete: (entry: ITimeEntry) => void;
  isVisible: boolean;
  onDismiss: () => void;
}

interface ITimerSession {
  projectId: number;
  projectTitle: string;
  workType: WorkType;
  description: string;
  startTime: Date;
  elapsedSeconds: number;
  isRunning: boolean;
}

const StopwatchTimer: React.FC<IStopwatchTimerProps> = ({
  projects,
  onTimeEntryComplete,
  isVisible,
  onDismiss
}) => {
  const [currentSession, setCurrentSession] = useState<ITimerSession | null>(null);
  const [selectedProject, setSelectedProject] = useState<number | undefined>();
  const [selectedWorkType, setSelectedWorkType] = useState<WorkType>(WorkType.Office);
  const [description, setDescription] = useState<string>('');
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);
  const [displayTime, setDisplayTime] = useState<string>('00:00:00');
  
  const intervalRef = useRef<number | null>(null);

  useEffect(() => {
    if (currentSession?.isRunning) {
      intervalRef.current = setInterval(() => {
        setCurrentSession(prev => {
          if (!prev) return null;
          const newElapsed = prev.elapsedSeconds + 1;
          setDisplayTime(formatTime(newElapsed));
          return { ...prev, elapsedSeconds: newElapsed };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [currentSession?.isRunning]);

  useEffect(() => {
    if (currentSession) {
      setDisplayTime(formatTime(currentSession.elapsedSeconds));
    } else {
      setDisplayTime('00:00:00');
    }
  }, [currentSession]);

  const formatTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const startTimer = (): void => {
    if (!selectedProject) {
      setMessage({
        text: 'Please select a project before starting the timer',
        type: MessageBarType.error
      });
      return;
    }

    const project = projects.find(p => p.id === selectedProject);
    if (!project) return;

    const newSession: ITimerSession = {
      projectId: selectedProject,
      projectTitle: project.title,
      workType: selectedWorkType,
      description: description,
      startTime: new Date(),
      elapsedSeconds: 0,
      isRunning: true
    };

    setCurrentSession(newSession);
    setMessage(null);
  };

  const pauseTimer = (): void => {
    if (currentSession) {
      setCurrentSession({ ...currentSession, isRunning: false });
    }
  };

  const resumeTimer = (): void => {
    if (currentSession) {
      setCurrentSession({ ...currentSession, isRunning: true });
    }
  };

  const stopTimer = (): void => {
    if (!currentSession) return;

    const hours = currentSession.elapsedSeconds / 3600;
    
    if (hours < 0.01) { // Less than 36 seconds (0.01 hours)
      setMessage({
        text: 'Timer session too short. Minimum time is 0.01 hours (36 seconds).',
        type: MessageBarType.warning
      });
      return;
    }

    const timeEntry: ITimeEntry = {
      projectId: currentSession.projectId,
      projectTitle: currentSession.projectTitle,
      date: new Date(),
      hours: Math.round(hours * 100) / 100, // Round to 2 decimal places
      workType: currentSession.workType,
      description: currentSession.description,
      startTime: currentSession.startTime,
      endTime: new Date()
    };

    onTimeEntryComplete(timeEntry);
    resetTimer();
    
    setMessage({
      text: `Time entry saved: ${timeEntry.hours} hours for ${timeEntry.projectTitle}`,
      type: MessageBarType.success
    });
  };

  const resetTimer = (): void => {
    setCurrentSession(null);
    setSelectedProject(undefined);
    setSelectedWorkType(WorkType.Office);
    setDescription('');
    setDisplayTime('00:00:00');
  };

  const discardTimer = (): void => {
    resetTimer();
    setMessage({
      text: 'Timer session discarded',
      type: MessageBarType.info
    });
  };

  const projectOptions: IDropdownOption[] = projects.map(project => ({
    key: project.id!,
    text: project.title
  }));

  const workTypeOptions: IDropdownOption[] = [
    { key: WorkType.Office, text: 'Office' },
    { key: WorkType.Onsite, text: 'Onsite' },
    { key: WorkType.WFH, text: 'Work From Home' }
  ];

  const renderTimerDisplay = (): JSX.Element => (
    <div className={styles.timerDisplay}>
      <Text variant="xxLarge" className={styles.timerText}>
        {displayTime}
      </Text>
      {currentSession && (
        <div className={styles.timerInfo}>
          <Text variant="medium" block>
            <strong>Project:</strong> {currentSession.projectTitle}
          </Text>
          <Text variant="medium" block>
            <strong>Work Type:</strong> {currentSession.workType}
          </Text>
          <Text variant="medium" block>
            <strong>Started:</strong> {currentSession.startTime.toLocaleTimeString()}
          </Text>
          {currentSession.description && (
            <Text variant="medium" block>
              <strong>Description:</strong> {currentSession.description}
            </Text>
          )}
        </div>
      )}
    </div>
  );

  const renderTimerControls = (): JSX.Element => {
    if (!currentSession) {
      return (
        <Stack tokens={{ childrenGap: 15 }}>
          <Dropdown
            label="Project"
            options={projectOptions}
            selectedKey={selectedProject}
            onChange={(_, option) => setSelectedProject(option?.key as number)}
            placeholder="Select a project"
            required
          />
          <Dropdown
            label="Work Type"
            options={workTypeOptions}
            selectedKey={selectedWorkType}
            onChange={(_, option) => setSelectedWorkType(option?.key as WorkType)}
            required
          />
          <TextField
            label="Description (Optional)"
            value={description}
            onChange={(_, newValue) => setDescription(newValue || '')}
            multiline
            rows={3}
            placeholder="Describe what you're working on..."
          />
          <PrimaryButton
            text="Start Timer"
            iconProps={{ iconName: 'Play' }}
            onClick={startTimer}
            disabled={!selectedProject}
          />
        </Stack>
      );
    }

    return (
      <Stack tokens={{ childrenGap: 10 }} horizontal horizontalAlign="center">
        {currentSession.isRunning ? (
          <PrimaryButton
            text="Pause"
            iconProps={{ iconName: 'Pause' }}
            onClick={pauseTimer}
          />
        ) : (
          <PrimaryButton
            text="Resume"
            iconProps={{ iconName: 'Play' }}
            onClick={resumeTimer}
          />
        )}
        <DefaultButton
          text="Stop & Save"
          iconProps={{ iconName: 'Stop' }}
          onClick={stopTimer}
        />
        <DefaultButton
          text="Discard"
          iconProps={{ iconName: 'Delete' }}
          onClick={discardTimer}
          styles={{ root: { color: '#a80000' } }}
        />
      </Stack>
    );
  };

  return (
    <Panel
      isOpen={isVisible}
      onDismiss={onDismiss}
      type={PanelType.medium}
      headerText="Stopwatch Timer"
      closeButtonAriaLabel="Close"
    >
      <div className={styles.stopwatchContainer}>
        {message && (
          <MessageBar
            messageBarType={message.type}
            onDismiss={() => setMessage(null)}
            dismissButtonAriaLabel="Close"
          >
            {message.text}
          </MessageBar>
        )}

        {renderTimerDisplay()}
        
        <div className={styles.timerControls}>
          {renderTimerControls()}
        </div>

        <div className={styles.timerInstructions}>
          <Text variant="small" style={{ color: '#605e5c' }}>
            <strong>Instructions:</strong>
            <br />
            • Select a project and work type before starting
            • Use the timer to track your work sessions
            • Pause/resume as needed during your work
            • Stop and save when you're done to create a time entry
            • Minimum session time is 0.01 hours (36 seconds)
          </Text>
        </div>
      </div>
    </Panel>
  );
};

export default StopwatchTimer;
