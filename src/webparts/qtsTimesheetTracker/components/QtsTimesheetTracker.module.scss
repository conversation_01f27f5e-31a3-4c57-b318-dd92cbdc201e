@import '~@fluentui/react/dist/sass/References.scss';

.qtsTimesheetTracker {
  color: "[theme:bodyText, default: #323130]";
  color: var(--bodyText);
  padding: 20px;
  height: 100%;
  
  &.teams {
    font-family: $ms-font-family-fallbacks;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--neutralQuaternaryAlt);

    .title {
      font-size: $ms-font-size-xxl;
      font-weight: $ms-font-weight-semibold;
      color: "[theme:themePrimary, default: #0078d4]";
      color: var(--themePrimary);
      margin: 0;
    }

    .userInfo {
      font-size: $ms-font-size-s;
      color: "[theme:bodyTextChecked, default: #605e5c]";
      color: var(--bodyTextChecked);
    }
  }

  .loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .pivot {
    .pivotItem {
      padding: 20px 0;
    }
  }
}

// Common styles for all views
.viewContainer {
  padding: 20px 0;
  height: calc(100vh - 200px);
  overflow-y: auto;

  .sectionTitle {
    font-size: $ms-font-size-xl;
    font-weight: $ms-font-weight-semibold;
    margin-bottom: 16px;
    color: "[theme:bodyText, default: #323130]";
    color: var(--bodyText);
  }

  .actionBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
    background-color: var(--neutralLighterAlt);
    border-radius: 4px;

    .actionButtons {
      display: flex;
      gap: 10px;
    }
  }

  .summaryCards {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .summaryCard {
      background-color: "[theme:white, default: #ffffff]";
      background-color: var(--white);
      border: 1px solid var(--neutralQuaternaryAlt);
      border-radius: 4px;
      padding: 16px;
      min-width: 200px;
      flex: 1;

      .cardTitle {
        font-size: $ms-font-size-s;
        color: "[theme:bodyTextChecked, default: #605e5c]";
        color: var(--bodyTextChecked);
        margin-bottom: 4px;
      }

      .cardValue {
        font-size: $ms-font-size-xl;
        font-weight: $ms-font-weight-semibold;
        color: "[theme:themePrimary, default: #0078d4]";
        color: var(--themePrimary);
      }
    }
  }

  .emptyState {
    text-align: center;
    padding: 40px 20px;
    color: "[theme:bodyTextChecked, default: #605e5c]";
    color: var(--bodyTextChecked);

    .emptyTitle {
      font-size: $ms-font-size-l;
      font-weight: $ms-font-weight-semibold;
      margin-bottom: 8px;
    }

    .emptyDescription {
      font-size: $ms-font-size-m;
      margin-bottom: 20px;
    }
  }
}

// Component-specific styles
.commandBar {
  margin-bottom: 16px;
}

.dataGrid {
  border: 1px solid var(--neutralQuaternaryAlt);
  border-radius: 4px;
}

.timesheetHeader {
  background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
  background-color: var(--neutralLighterAlt);
  padding: 12px;
  border-bottom: 1px solid var(--neutralQuaternaryAlt);
  font-weight: $ms-font-weight-semibold;
}

.timesheetGrid {
  .timesheetRow {
    border-bottom: 1px solid var(--neutralQuaternaryAlt);
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .timesheetCell {
    padding: 8px 12px;
    border-right: 1px solid var(--neutralQuaternaryAlt);
    
    &:last-child {
      border-right: none;
    }
  }
}

// Responsive design
@media screen and (max-width: 768px) {
  .qtsTimesheetTracker {
    padding: 10px;

    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .title {
        font-size: $ms-font-size-xl;
      }
    }

    .viewContainer {
      .actionBar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;

        .actionButtons {
          justify-content: center;
        }
      }

      .summaryCards {
        flex-direction: column;
      }
    }
  }
}

// Time Tracking Specific Styles
.timesheetGrid {
  .gridHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .detailsList {
    margin-bottom: 20px;

    :global(.ms-DetailsHeader) {
      background-color: "[theme:neutralLighter, default: #f3f2f1]";
      background-color: var(--neutralLighter);
    }
  }
}

.stopwatchContainer {
  padding: 20px;

  .timerDisplay {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: "[theme:neutralLighter, default: #f3f2f1]";
    background-color: var(--neutralLighter);
    border-radius: 8px;

    .timerText {
      font-family: 'Courier New', monospace;
      font-weight: bold;
      color: "[theme:themePrimary, default: #0078d4]";
      color: var(--themePrimary);
      margin-bottom: 15px;
    }

    .timerInfo {
      text-align: left;
      max-width: 300px;
      margin: 0 auto;

      > * {
        margin-bottom: 5px;
      }
    }
  }

  .timerControls {
    margin-bottom: 30px;
  }

  .timerInstructions {
    padding: 15px;
    background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
    background-color: var(--neutralLighterAlt);
    border-radius: 4px;
    border-left: 4px solid "[theme:themePrimary, default: #0078d4]";
    border-left-color: var(--themePrimary);
  }
}

.quickTimeEntryContainer {
  padding: 20px;

  .quickEntryInstructions {
    margin-top: 30px;
    padding: 15px;
    background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
    background-color: var(--neutralLighterAlt);
    border-radius: 4px;
    border-left: 4px solid "[theme:themePrimary, default: #0078d4]";
    border-left-color: var(--themePrimary);
  }
}