@import '~@fluentui/react/dist/sass/References.scss';

.qtsTimesheetTracker {
  color: "[theme:bodyText, default: #323130]";
  color: var(--bodyText);
  padding: 20px;
  height: 100%;
  
  &.teams {
    font-family: $ms-font-family-fallbacks;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--neutralQuaternaryAlt);

    .title {
      font-size: $ms-font-size-xxl;
      font-weight: $ms-font-weight-semibold;
      color: "[theme:themePrimary, default: #0078d4]";
      color: var(--themePrimary);
      margin: 0;
    }

    .userInfo {
      font-size: $ms-font-size-s;
      color: "[theme:bodyTextChecked, default: #605e5c]";
      color: var(--bodyTextChecked);
    }
  }

  .loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .pivot {
    .pivotItem {
      padding: 20px 0;
    }
  }
}

// Common styles for all views
.viewContainer {
  padding: 20px 0;
  height: calc(100vh - 200px);
  overflow-y: auto;

  .sectionTitle {
    font-size: $ms-font-size-xl;
    font-weight: $ms-font-weight-semibold;
    margin-bottom: 16px;
    color: "[theme:bodyText, default: #323130]";
    color: var(--bodyText);
  }

  .actionBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
    background-color: var(--neutralLighterAlt);
    border-radius: 4px;

    .actionButtons {
      display: flex;
      gap: 10px;
    }
  }

  .summaryCards {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .summaryCard {
      background-color: "[theme:white, default: #ffffff]";
      background-color: var(--white);
      border: 1px solid var(--neutralQuaternaryAlt);
      border-radius: 4px;
      padding: 16px;
      min-width: 200px;
      flex: 1;

      .cardTitle {
        font-size: $ms-font-size-s;
        color: "[theme:bodyTextChecked, default: #605e5c]";
        color: var(--bodyTextChecked);
        margin-bottom: 4px;
      }

      .cardValue {
        font-size: $ms-font-size-xl;
        font-weight: $ms-font-weight-semibold;
        color: "[theme:themePrimary, default: #0078d4]";
        color: var(--themePrimary);
      }
    }
  }

  .emptyState {
    text-align: center;
    padding: 40px 20px;
    color: "[theme:bodyTextChecked, default: #605e5c]";
    color: var(--bodyTextChecked);

    .emptyTitle {
      font-size: $ms-font-size-l;
      font-weight: $ms-font-weight-semibold;
      margin-bottom: 8px;
    }

    .emptyDescription {
      font-size: $ms-font-size-m;
      margin-bottom: 20px;
    }
  }
}

// Component-specific styles
.commandBar {
  margin-bottom: 16px;
}

.dataGrid {
  border: 1px solid var(--neutralQuaternaryAlt);
  border-radius: 4px;
}

.timesheetHeader {
  background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
  background-color: var(--neutralLighterAlt);
  padding: 12px;
  border-bottom: 1px solid var(--neutralQuaternaryAlt);
  font-weight: $ms-font-weight-semibold;
}

.timesheetGrid {
  .timesheetRow {
    border-bottom: 1px solid var(--neutralQuaternaryAlt);
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .timesheetCell {
    padding: 8px 12px;
    border-right: 1px solid var(--neutralQuaternaryAlt);
    
    &:last-child {
      border-right: none;
    }
  }
}

// Responsive design
@media screen and (max-width: 768px) {
  .qtsTimesheetTracker {
    padding: 10px;

    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .title {
        font-size: $ms-font-size-xl;
      }
    }

    .viewContainer {
      .actionBar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;

        .actionButtons {
          justify-content: center;
        }
      }

      .summaryCards {
        flex-direction: column;
      }
    }
  }
}

// Time Tracking Specific Styles
.timesheetGrid {
  .gridHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .detailsList {
    margin-bottom: 20px;

    :global(.ms-DetailsHeader) {
      background-color: "[theme:neutralLighter, default: #f3f2f1]";
      background-color: var(--neutralLighter);
    }
  }
}

.stopwatchContainer {
  padding: 20px;

  .timerDisplay {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: "[theme:neutralLighter, default: #f3f2f1]";
    background-color: var(--neutralLighter);
    border-radius: 8px;

    .timerText {
      font-family: 'Courier New', monospace;
      font-weight: bold;
      color: "[theme:themePrimary, default: #0078d4]";
      color: var(--themePrimary);
      margin-bottom: 15px;
    }

    .timerInfo {
      text-align: left;
      max-width: 300px;
      margin: 0 auto;

      > * {
        margin-bottom: 5px;
      }
    }
  }

  .timerControls {
    margin-bottom: 30px;
  }

  .timerInstructions {
    padding: 15px;
    background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
    background-color: var(--neutralLighterAlt);
    border-radius: 4px;
    border-left: 4px solid "[theme:themePrimary, default: #0078d4]";
    border-left-color: var(--themePrimary);
  }
}

.quickTimeEntryContainer {
  padding: 20px;

  .quickEntryInstructions {
    margin-top: 30px;
    padding: 15px;
    background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
    background-color: var(--neutralLighterAlt);
    border-radius: 4px;
    border-left: 4px solid "[theme:themePrimary, default: #0078d4]";
    border-left-color: var(--themePrimary);
  }
}

// Cost Management Styles
.costSummary {
  .workTypeBreakdown {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .workTypeItem {
      padding: 10px;
      background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
      background-color: var(--neutralLighterAlt);
      border-radius: 4px;
      border-left: 3px solid "[theme:themePrimary, default: #0078d4]";
      border-left-color: var(--themePrimary);
    }
  }
}

// Rate Management Styles
.rateManagement {
  .rateCard {
    padding: 15px;
    background-color: "[theme:neutralLighter, default: #f3f2f1]";
    background-color: var(--neutralLighter);
    border-radius: 8px;
    margin-bottom: 15px;

    .rateHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .rateValues {
      display: flex;
      gap: 20px;

      .rateValue {
        text-align: center;

        .rateAmount {
          font-size: $ms-font-size-xl;
          font-weight: $ms-font-weight-bold;
          color: "[theme:themePrimary, default: #0078d4]";
          color: var(--themePrimary);
        }

        .rateLabel {
          font-size: $ms-font-size-s;
          color: "[theme:bodyTextChecked, default: #605e5c]";
          color: var(--bodyTextChecked);
        }
      }
    }
  }
}

// Filters Section
.filtersSection {
  padding: 20px;
  background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
  background-color: var(--neutralLighterAlt);
  border-radius: 8px;
  margin-bottom: 20px;
}

// Mobile Responsiveness
@media (max-width: 768px) {
  .qtsTimesheetTracker {
    padding: 10px;
  }

  .sectionTitle {
    font-size: $ms-font-size-xl;
    margin-bottom: 15px;
  }

  .summaryCards {
    flex-direction: column;
    gap: 10px;

    .summaryCard {
      min-width: auto;
      padding: 15px;
    }
  }

  .commandBar {
    :global(.ms-CommandBar) {
      padding: 0;
    }

    :global(.ms-CommandBar-primaryCommands) {
      flex-wrap: wrap;
      gap: 5px;
    }
  }

  .filtersSection {
    padding: 15px;

    :global(.ms-Stack--horizontal) {
      flex-direction: column;
      gap: 10px;
    }
  }

  .detailsList {
    :global(.ms-DetailsHeader) {
      display: none; // Hide headers on mobile for better space usage
    }

    :global(.ms-DetailsRow) {
      border-bottom: 1px solid "[theme:neutralLight, default: #edebe9]";
      border-bottom-color: var(--neutralLight);
      padding: 10px 0;
    }

    :global(.ms-DetailsRow-cell) {
      padding: 5px;
      font-size: $ms-font-size-s;
    }
  }

  .timesheetGrid {
    .gridHeader {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }

  .stopwatchContainer {
    padding: 15px;

    .timerDisplay {
      padding: 15px;

      .timerText {
        font-size: $ms-font-size-xxl;
      }
    }

    .timerControls {
      :global(.ms-Stack--horizontal) {
        flex-direction: column;
        gap: 10px;
      }
    }
  }

  .quickTimeEntryContainer {
    padding: 15px;
  }

  // Panel adjustments for mobile
  :global(.ms-Panel-main) {
    width: 100% !important;
    max-width: 100% !important;
  }

  :global(.ms-Panel-content) {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .qtsTimesheetTracker {
    padding: 5px;
  }

  .summaryCard {
    .cardValue {
      font-size: $ms-font-size-xl;
    }
  }

  .timerDisplay {
    .timerText {
      font-size: $ms-font-size-xl !important;
    }
  }

  // Stack items vertically on very small screens
  :global(.ms-Stack--horizontal) {
    flex-direction: column !important;
    gap: 10px !important;
  }
}

// Enhanced UI Polish
.qtsTimesheetTracker {
  // Smooth transitions
  * {
    transition: all 0.2s ease-in-out;
  }

  // Enhanced focus states
  :global(.ms-Button:focus) {
    outline: 2px solid "[theme:themePrimary, default: #0078d4]";
    outline-color: var(--themePrimary);
    outline-offset: 2px;
  }

  // Enhanced hover states
  :global(.ms-Button:hover) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  // Card hover effects
  .summaryCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  // Enhanced loading states
  .loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
    gap: 15px;

    .loadingText {
      color: "[theme:bodyTextChecked, default: #605e5c]";
      color: var(--bodyTextChecked);
    }
  }

  // Enhanced empty states
  .emptyState {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg,
      "[theme:neutralLighterAlt, default: #faf9f8]" 0%,
      "[theme:neutralLighter, default: #f3f2f1]" 100%);
    background: linear-gradient(135deg,
      var(--neutralLighterAlt) 0%,
      var(--neutralLighter) 100%);
    border-radius: 12px;
    border: 1px solid "[theme:neutralLight, default: #edebe9]";
    border-color: var(--neutralLight);

    .emptyTitle {
      font-size: $ms-font-size-xl;
      font-weight: $ms-font-weight-semibold;
      color: "[theme:bodyText, default: #323130]";
      color: var(--bodyText);
      margin-bottom: 10px;
    }

    .emptyDescription {
      font-size: $ms-font-size-m;
      color: "[theme:bodyTextChecked, default: #605e5c]";
      color: var(--bodyTextChecked);
      margin-bottom: 20px;
      line-height: 1.5;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  // Enhanced message bars
  :global(.ms-MessageBar) {
    border-radius: 6px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  // Enhanced panels
  :global(.ms-Panel) {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  :global(.ms-Panel-header) {
    border-bottom: 1px solid "[theme:neutralLight, default: #edebe9]";
    border-bottom-color: var(--neutralLight);
  }

  // Enhanced dialogs
  :global(.ms-Dialog-main) {
    border-radius: 8px;
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
  }

  // Enhanced form controls
  :global(.ms-TextField-field) {
    border-radius: 4px;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  :global(.ms-TextField-field:focus) {
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
  }

  :global(.ms-Dropdown) {
    border-radius: 4px;
  }

  // Enhanced tables
  :global(.ms-DetailsList) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  :global(.ms-DetailsHeader) {
    background: linear-gradient(135deg,
      "[theme:neutralLighter, default: #f3f2f1]" 0%,
      "[theme:neutralLight, default: #edebe9]" 100%);
    background: linear-gradient(135deg,
      var(--neutralLighter) 0%,
      var(--neutralLight) 100%);
  }

  :global(.ms-DetailsRow:hover) {
    background-color: "[theme:neutralLighterAlt, default: #faf9f8]";
    background-color: var(--neutralLighterAlt);
  }
}