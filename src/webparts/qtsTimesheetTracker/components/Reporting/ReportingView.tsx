import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  MessageBar,
  MessageBarType,
  CommandBar,
  ICommandBarItemProps,
  Text,
  Pivot,
  PivotItem,
  Stack,
  Dropdown,
  IDropdownOption,
  DatePicker,
  DetailsList,
  IColumn,
  DetailsListLayoutMode,
  SelectionMode,
  Spinner,
  SpinnerSize,
  Checkbox
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject, ITimesheet, ITimeEntry, TimesheetStatus } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import { DateUtils } from '../../utils/DateUtils';
import CostSummary from '../Common/CostSummary';
import styles from '../QtsTimesheetTracker.module.scss';

interface IReportingViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
}

interface IReportFilters {
  reportType: 'daily' | 'weekly' | 'monthly' | 'team' | 'project' | 'pending';
  startDate: Date;
  endDate: Date;
  selectedProjects: number[];
  selectedUsers: string[];
  includeApproved: boolean;
  includePending: boolean;
  includeRejected: boolean;
}

interface IReportData {
  timesheets: ITimesheet[];
  timeEntries: ITimeEntry[];
  totalHours: number;
  totalCost: number;
  summary: {
    totalUsers: number;
    totalProjects: number;
    averageHoursPerUser: number;
    completionRate: number;
  };
}

const ReportingView: React.FC<IReportingViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService
}) => {
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedTab, setSelectedTab] = useState<string>('overview');
  const [reportData, setReportData] = useState<IReportData | null>(null);
  const [filters, setFilters] = useState<IReportFilters>({
    reportType: 'weekly',
    startDate: DateUtils.getWeekStart(new Date()),
    endDate: DateUtils.getWeekEnd(new Date()),
    selectedProjects: [],
    selectedUsers: [],
    includeApproved: true,
    includePending: true,
    includeRejected: false
  });

  useEffect(() => {
    generateReport().catch(console.error);
  }, [filters]);

  const generateReport = async (): Promise<void> => {
    try {
      setLoading(true);

      // Get timesheets based on filters
      const timesheets = await sharePointService.getTimesheets();

      // Filter timesheets based on criteria
      const filteredTimesheets = timesheets.filter(timesheet => {
        // Date range filter
        const timesheetDate = timesheet.weekStartDate;
        if (timesheetDate < filters.startDate || timesheetDate > filters.endDate) {
          return false;
        }

        // Status filter
        if (!filters.includeApproved && timesheet.status === TimesheetStatus.Approved) return false;
        if (!filters.includePending && timesheet.status === TimesheetStatus.Submitted) return false;
        if (!filters.includeRejected && timesheet.status === TimesheetStatus.Rejected) return false;

        // User filter
        if (filters.selectedUsers.length > 0 && !filters.selectedUsers.includes(timesheet.userId)) {
          return false;
        }

        return true;
      });

      // Extract time entries and filter by projects
      const allTimeEntries: ITimeEntry[] = [];
      filteredTimesheets.forEach(timesheet => {
        const filteredEntries = timesheet.timeEntries.filter(entry => {
          if (filters.selectedProjects.length > 0) {
            return filters.selectedProjects.includes(entry.projectId);
          }
          return true;
        });
        allTimeEntries.push(...filteredEntries);
      });

      // Calculate summary statistics
      const totalHours = allTimeEntries.reduce((sum, entry) => sum + entry.hours, 0);
      const uniqueUsers = new Set(filteredTimesheets.map(ts => ts.userId)).size;
      const uniqueProjects = new Set(allTimeEntries.map(entry => entry.projectId)).size;
      const averageHoursPerUser = uniqueUsers > 0 ? totalHours / uniqueUsers : 0;

      // Calculate completion rate (approved vs total)
      const approvedTimesheets = filteredTimesheets.filter(ts => ts.status === TimesheetStatus.Approved).length;
      const completionRate = filteredTimesheets.length > 0 ? (approvedTimesheets / filteredTimesheets.length) * 100 : 0;

      const reportData: IReportData = {
        timesheets: filteredTimesheets,
        timeEntries: allTimeEntries,
        totalHours,
        totalCost: 0, // Will be calculated by CostSummary component
        summary: {
          totalUsers: uniqueUsers,
          totalProjects: uniqueProjects,
          averageHoursPerUser,
          completionRate
        }
      };

      setReportData(reportData);

    } catch (error) {
      setMessage({
        text: 'Failed to generate report. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error generating report:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = (format: 'csv' | 'excel' | 'pdf'): void => {
    if (!reportData) return;

    try {
      let content = '';
      let filename = '';
      let mimeType = '';

      switch (format) {
        case 'csv':
          content = generateCsvReport(reportData);
          filename = `timesheet-report-${DateUtils.formatDate(new Date())}.csv`;
          mimeType = 'text/csv';
          break;
        case 'excel':
          // For now, export as CSV (Excel format would require additional library)
          content = generateCsvReport(reportData);
          filename = `timesheet-report-${DateUtils.formatDate(new Date())}.csv`;
          mimeType = 'text/csv';
          break;
        case 'pdf':
          setMessage({
            text: 'PDF export is not yet implemented. Please use CSV export.',
            type: MessageBarType.info
          });
          return;
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setMessage({
        text: `Report exported successfully as ${format.toUpperCase()}`,
        type: MessageBarType.success
      });
    } catch (error) {
      setMessage({
        text: 'Failed to export report',
        type: MessageBarType.error
      });
      console.error('Export error:', error);
    }
  };

  const generateCsvReport = (data: IReportData): string => {
    const headers = ['User', 'Project', 'Date', 'Hours', 'Work Type', 'Status', 'Description'];
    const rows: string[][] = [headers];

    data.timesheets.forEach(timesheet => {
      timesheet.timeEntries.forEach(entry => {
        rows.push([
          timesheet.userName,
          entry.projectTitle,
          DateUtils.formatDate(entry.date),
          entry.hours.toString(),
          entry.workType,
          timesheet.status,
          entry.description || ''
        ]);
      });
    });

    // Add summary rows
    rows.push(['', '', '', '', '', '', '']);
    rows.push(['SUMMARY', '', '', '', '', '', '']);
    rows.push(['Total Hours', '', '', data.totalHours.toString(), '', '', '']);
    rows.push(['Total Users', '', '', data.summary.totalUsers.toString(), '', '', '']);
    rows.push(['Total Projects', '', '', data.summary.totalProjects.toString(), '', '', '']);
    rows.push(['Completion Rate', '', '', `${data.summary.completionRate.toFixed(1)}%`, '', '', '']);

    return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  };

  const commandBarItems: ICommandBarItemProps[] = [
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => { generateReport().catch(console.error); }
    },
    {
      key: 'export',
      text: 'Export',
      iconProps: { iconName: 'Download' },
      subMenuProps: {
        items: [
          {
            key: 'exportCsv',
            text: 'Export as CSV',
            iconProps: { iconName: 'ExcelDocument' },
            onClick: () => exportReport('csv')
          },
          {
            key: 'exportExcel',
            text: 'Export as Excel',
            iconProps: { iconName: 'ExcelDocument' },
            onClick: () => exportReport('excel')
          },
          {
            key: 'exportPdf',
            text: 'Export as PDF',
            iconProps: { iconName: 'PDF' },
            onClick: () => exportReport('pdf')
          }
        ]
      }
    }
  ];

  const reportTypeOptions: IDropdownOption[] = [
    { key: 'daily', text: 'Daily Report' },
    { key: 'weekly', text: 'Weekly Report' },
    { key: 'monthly', text: 'Monthly Report' },
    { key: 'team', text: 'Team Report' },
    { key: 'project', text: 'Project Report' },
    { key: 'pending', text: 'Pending Reports' }
  ];

  const projectOptions: IDropdownOption[] = projects.map(project => ({
    key: project.id!,
    text: project.title
  }));

  const timesheetColumns: IColumn[] = [
    {
      key: 'user',
      name: 'User',
      fieldName: 'userName',
      minWidth: 120,
      maxWidth: 180,
      isResizable: true
    },
    {
      key: 'week',
      name: 'Week',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small">
          {DateUtils.formatWeekRange(item.weekStartDate, item.weekEndDate)}
        </Text>
      )
    },
    {
      key: 'hours',
      name: 'Total Hours',
      fieldName: 'totalHours',
      minWidth: 80,
      maxWidth: 100,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small">{item.totalHours.toFixed(2)}</Text>
      )
    },
    {
      key: 'status',
      name: 'Status',
      fieldName: 'status',
      minWidth: 80,
      maxWidth: 100,
      isResizable: true,
      onRender: (item: ITimesheet) => {
        const getStatusColor = (status: TimesheetStatus): string => {
          switch (status) {
            case TimesheetStatus.Draft: return '#605e5c';
            case TimesheetStatus.Submitted: return '#d83b01';
            case TimesheetStatus.Approved: return '#107c10';
            case TimesheetStatus.Rejected: return '#a80000';
            default: return '#605e5c';
          }
        };

        return (
          <Text variant="small" style={{ color: getStatusColor(item.status), fontWeight: 'bold' }}>
            {item.status}
          </Text>
        );
      }
    },
    {
      key: 'submitted',
      name: 'Submitted',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small">
          {item.submittedDate ? DateUtils.formatDisplayDate(item.submittedDate) : '-'}
        </Text>
      )
    },
    {
      key: 'approved',
      name: 'Approved',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small">
          {item.approvedDate ? DateUtils.formatDisplayDate(item.approvedDate) : '-'}
        </Text>
      )
    }
  ];

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>Reports & Analytics</div>

      <CommandBar
        items={commandBarItems}
        className={styles.commandBar}
      />

      <Pivot
        selectedKey={selectedTab}
        onLinkClick={(item) => item?.props.itemKey && setSelectedTab(item.props.itemKey)}
        headersOnly={true}
        className={styles.pivot}
      >
        <PivotItem headerText="Overview" itemKey="overview">
          <Stack tokens={{ childrenGap: 20 }}>
            {/* Filters Section */}
            <div className={styles.filtersSection}>
              <Text variant="medium" style={{ fontWeight: 'bold', marginBottom: '15px' }}>
                Report Filters
              </Text>

              <Stack horizontal wrap tokens={{ childrenGap: 15 }}>
                <Stack.Item>
                  <Dropdown
                    label="Report Type"
                    options={reportTypeOptions}
                    selectedKey={filters.reportType}
                    onChange={(_, option) => setFilters({
                      ...filters,
                      reportType: option?.key as any
                    })}
                    styles={{ dropdown: { width: 150 } }}
                  />
                </Stack.Item>

                <Stack.Item>
                  <DatePicker
                    label="Start Date"
                    value={filters.startDate}
                    onSelectDate={(date) => date && setFilters({
                      ...filters,
                      startDate: date
                    })}
                  />
                </Stack.Item>

                <Stack.Item>
                  <DatePicker
                    label="End Date"
                    value={filters.endDate}
                    onSelectDate={(date) => date && setFilters({
                      ...filters,
                      endDate: date
                    })}
                  />
                </Stack.Item>

                <Stack.Item>
                  <Dropdown
                    label="Projects"
                    options={projectOptions}
                    multiSelect
                    selectedKeys={filters.selectedProjects}
                    onChange={(_, option) => {
                      const selectedKeys = filters.selectedProjects.slice();
                      if (option) {
                        if (option.selected) {
                          selectedKeys.push(option.key as number);
                        } else {
                          const index = selectedKeys.indexOf(option.key as number);
                          if (index > -1) {
                            selectedKeys.splice(index, 1);
                          }
                        }
                      }
                      setFilters({ ...filters, selectedProjects: selectedKeys });
                    }}
                    placeholder="All Projects"
                    styles={{ dropdown: { width: 200 } }}
                  />
                </Stack.Item>
              </Stack>

              <Stack horizontal tokens={{ childrenGap: 15 }} style={{ marginTop: '15px' }}>
                <Checkbox
                  label="Include Approved"
                  checked={filters.includeApproved}
                  onChange={(_, checked) => setFilters({
                    ...filters,
                    includeApproved: !!checked
                  })}
                />
                <Checkbox
                  label="Include Pending"
                  checked={filters.includePending}
                  onChange={(_, checked) => setFilters({
                    ...filters,
                    includePending: !!checked
                  })}
                />
                <Checkbox
                  label="Include Rejected"
                  checked={filters.includeRejected}
                  onChange={(_, checked) => setFilters({
                    ...filters,
                    includeRejected: !!checked
                  })}
                />
              </Stack>
            </div>

            {loading && (
              <div className={styles.loadingContainer}>
                <Spinner size={SpinnerSize.large} label="Generating report..." />
              </div>
            )}

            {/* Summary Cards */}
            {reportData && !loading && (
              <>
                <div className={styles.summaryCards}>
                  <div className={styles.summaryCard}>
                    <div className={styles.cardTitle}>Total Hours</div>
                    <div className={styles.cardValue}>{reportData.totalHours.toFixed(2)}</div>
                  </div>
                  <div className={styles.summaryCard}>
                    <div className={styles.cardTitle}>Total Users</div>
                    <div className={styles.cardValue}>{reportData.summary.totalUsers}</div>
                  </div>
                  <div className={styles.summaryCard}>
                    <div className={styles.cardTitle}>Total Projects</div>
                    <div className={styles.cardValue}>{reportData.summary.totalProjects}</div>
                  </div>
                  <div className={styles.summaryCard}>
                    <div className={styles.cardTitle}>Completion Rate</div>
                    <div className={styles.cardValue}>{reportData.summary.completionRate.toFixed(1)}%</div>
                  </div>
                </div>

                {/* Cost Summary */}
                {reportData.timeEntries.length > 0 && (
                  <CostSummary
                    timeEntries={reportData.timeEntries}
                    projects={projects}
                    title="Cost Analysis"
                    showBreakdown={true}
                    showExport={false}
                  />
                )}
              </>
            )}
          </Stack>
        </PivotItem>

        <PivotItem headerText="Timesheets" itemKey="timesheets">
          {reportData && !loading ? (
            <DetailsList
              items={reportData.timesheets}
              columns={timesheetColumns}
              layoutMode={DetailsListLayoutMode.justified}
              selectionMode={SelectionMode.none}
              isHeaderVisible={true}
              className={styles.detailsList}
            />
          ) : loading ? (
            <div className={styles.loadingContainer}>
              <Spinner size={SpinnerSize.large} label="Loading timesheets..." />
            </div>
          ) : (
            <div className={styles.emptyState}>
              <div className={styles.emptyTitle}>No Data Available</div>
              <div className={styles.emptyDescription}>
                Adjust your filters to see timesheet data.
              </div>
            </div>
          )}
        </PivotItem>

        <PivotItem headerText="Power BI" itemKey="powerbi">
          <div className={styles.emptyState}>
            <div className={styles.emptyTitle}>Power BI Integration</div>
            <div className={styles.emptyDescription}>
              Power BI integration will allow you to create advanced dashboards and analytics.
              This feature will be available in a future update.
            </div>
            <div style={{ textAlign: 'left', marginTop: '20px' }}>
              <strong>Planned Features:</strong>
              <ul style={{ marginTop: '10px' }}>
                <li>Real-time data connection to SharePoint lists</li>
                <li>Pre-built dashboard templates</li>
                <li>Custom report builder</li>
                <li>Automated report scheduling</li>
                <li>Advanced analytics and insights</li>
              </ul>
            </div>
          </div>
        </PivotItem>
      </Pivot>
    </div>
  );
};

export default ReportingView;