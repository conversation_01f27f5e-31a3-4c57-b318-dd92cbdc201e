import * as React from 'react';
import { useState } from 'react';
import {
  MessageBar,
  MessageBarType
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import styles from '../QtsTimesheetTracker.module.scss';

interface IReportingViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
}

const ReportingView: React.FC<IReportingViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService
}) => {
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>Reports & Analytics</div>

      <div className={styles.emptyState}>
        <div className={styles.emptyTitle}>Reports Coming Soon</div>
        <div className={styles.emptyDescription}>
          Comprehensive reporting features including daily, weekly, monthly reports, 
          team reports, project reports, and Power BI integration will be implemented in the next phase.
        </div>
        <div style={{ textAlign: 'left', marginTop: '20px' }}>
          <strong>Planned Report Types:</strong>
          <ul style={{ marginTop: '10px' }}>
            <li>Daily Time Reports</li>
            <li>Weekly Time Reports</li>
            <li>Monthly Time Reports</li>
            <li>My Team Reports</li>
            <li>My Projects Reports</li>
            <li>Pending Weekly Reports</li>
            <li>Custom Reports with Filters</li>
            <li>Power BI Integration</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ReportingView;