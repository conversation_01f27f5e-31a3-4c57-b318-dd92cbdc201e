import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  MessageBar,
  MessageBarType,
  PrimaryButton,
  DefaultButton,
  CommandBar,
  ICommandBarItemProps,
  Text,
  DetailsList,
  IColumn,
  DetailsListLayoutMode,
  SelectionMode,
  Stack,
  Dropdown,
  IDropdownOption,
  TextField,
  Dialog,
  DialogFooter,
  DialogContent,
  Spinner,
  SpinnerSize,
  IconButton,
  TooltipHost,
  Panel,
  PanelType
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject, ITimesheet, TimesheetStatus, UserRole } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import { DateUtils } from '../../utils/DateUtils';
import CostSummary from '../Common/CostSummary';
import styles from '../QtsTimesheetTracker.module.scss';

interface IApprovalViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
}

interface IApprovalFilters {
  status: TimesheetStatus | 'all';
  userId: string | 'all';
  weekStartDate?: Date;
}

const ApprovalView: React.FC<IApprovalViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService
}) => {
  const [timesheets, setTimesheets] = useState<ITimesheet[]>([]);
  const [filteredTimesheets, setFilteredTimesheets] = useState<ITimesheet[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);
  const [selectedTimesheet, setSelectedTimesheet] = useState<ITimesheet | null>(null);
  const [showApprovalDialog, setShowApprovalDialog] = useState<boolean>(false);
  const [showRejectionDialog, setShowRejectionDialog] = useState<boolean>(false);
  const [showTimesheetPanel, setShowTimesheetPanel] = useState<boolean>(false);
  const [rejectionReason, setRejectionReason] = useState<string>('');
  const [filters, setFilters] = useState<IApprovalFilters>({
    status: TimesheetStatus.Submitted,
    userId: 'all'
  });

  useEffect(() => {
    loadTimesheets().catch(console.error);
  }, []);

  useEffect(() => {
    applyFilters();
  }, [timesheets, filters]);

  const loadTimesheets = async (): Promise<void> => {
    try {
      setLoading(true);
      const allTimesheets = await sharePointService.getTimesheets();

      // Filter based on user role
      let accessibleTimesheets = allTimesheets;

      if (currentUser.role === UserRole.ProjectManager) {
        // Project managers can only see timesheets from their team members
        accessibleTimesheets = allTimesheets.filter(() => true); // Simplified for now
      } else if (currentUser.role === UserRole.User) {
        // Regular users can only see their own timesheets
        accessibleTimesheets = allTimesheets.filter(ts => ts.userId === currentUser.id);
      }

      setTimesheets(accessibleTimesheets);
    } catch (error) {
      setMessage({
        text: 'Failed to load timesheets. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error loading timesheets:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = (): void => {
    let filtered = [...timesheets];

    if (filters.status !== 'all') {
      filtered = filtered.filter(ts => ts.status === filters.status);
    }

    if (filters.userId !== 'all') {
      filtered = filtered.filter(ts => ts.userId === filters.userId);
    }

    if (filters.weekStartDate) {
      filtered = filtered.filter(ts =>
        DateUtils.formatDate(ts.weekStartDate) === DateUtils.formatDate(filters.weekStartDate!)
      );
    }

    setFilteredTimesheets(filtered);
  };

  const approveTimesheet = async (timesheet: ITimesheet): Promise<void> => {
    try {
      setLoading(true);
      const updatedTimesheet: ITimesheet = {
        ...timesheet,
        status: TimesheetStatus.Approved,
        approvedDate: new Date(),
        approvedBy: currentUser.id,
        modified: new Date()
      };

      const success = await sharePointService.updateTimesheet(updatedTimesheet);

      if (success) {
        await loadTimesheets();
        setMessage({
          text: `Timesheet for ${timesheet.userName} approved successfully!`,
          type: MessageBarType.success
        });
        setShowApprovalDialog(false);
        setSelectedTimesheet(null);
      } else {
        setMessage({
          text: 'Failed to approve timesheet. Please try again.',
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error approving timesheet. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error approving timesheet:', error);
    } finally {
      setLoading(false);
    }
  };

  const rejectTimesheet = async (timesheet: ITimesheet, reason: string): Promise<void> => {
    if (!reason.trim()) {
      setMessage({
        text: 'Please provide a reason for rejection.',
        type: MessageBarType.error
      });
      return;
    }

    try {
      setLoading(true);
      const updatedTimesheet: ITimesheet = {
        ...timesheet,
        status: TimesheetStatus.Rejected,
        rejectedDate: new Date(),
        rejectedBy: currentUser.id,
        rejectionReason: reason.trim(),
        modified: new Date()
      };

      const success = await sharePointService.updateTimesheet(updatedTimesheet);

      if (success) {
        await loadTimesheets();
        setMessage({
          text: `Timesheet for ${timesheet.userName} rejected.`,
          type: MessageBarType.warning
        });
        setShowRejectionDialog(false);
        setSelectedTimesheet(null);
        setRejectionReason('');
      } else {
        setMessage({
          text: 'Failed to reject timesheet. Please try again.',
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error rejecting timesheet. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error rejecting timesheet:', error);
    } finally {
      setLoading(false);
    }
  };

  const openApprovalDialog = (timesheet: ITimesheet): void => {
    setSelectedTimesheet(timesheet);
    setShowApprovalDialog(true);
  };

  const openRejectionDialog = (timesheet: ITimesheet): void => {
    setSelectedTimesheet(timesheet);
    setShowRejectionDialog(true);
  };

  const openTimesheetDetails = (timesheet: ITimesheet): void => {
    setSelectedTimesheet(timesheet);
    setShowTimesheetPanel(true);
  };

  const canApproveReject = (timesheet: ITimesheet): boolean => {
    if (timesheet.userId === currentUser.id) return false;
    if (timesheet.status !== TimesheetStatus.Submitted) return false;

    return currentUser.role === UserRole.Administrator ||
           currentUser.role === UserRole.ProgramManager ||
           currentUser.role === UserRole.ProjectManager;
  };

  const getStatusColor = (status: TimesheetStatus): string => {
    switch (status) {
      case TimesheetStatus.Draft: return '#605e5c';
      case TimesheetStatus.Submitted: return '#d83b01';
      case TimesheetStatus.Approved: return '#107c10';
      case TimesheetStatus.Rejected: return '#a80000';
      default: return '#605e5c';
    }
  };

  const commandBarItems: ICommandBarItemProps[] = [
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => { loadTimesheets().catch(console.error); }
    }
  ];

  const statusOptions: IDropdownOption[] = [
    { key: 'all', text: 'All Statuses' },
    { key: TimesheetStatus.Submitted, text: 'Pending Approval' },
    { key: TimesheetStatus.Approved, text: 'Approved' },
    { key: TimesheetStatus.Rejected, text: 'Rejected' }
  ];

  // Get unique users for filter dropdown
  const uniqueUserIds = timesheets.reduce((acc: string[], ts) => {
    if (!acc.includes(ts.userId)) {
      acc.push(ts.userId);
    }
    return acc;
  }, []);

  const userOptions: IDropdownOption[] = [
    { key: 'all', text: 'All Users' },
    ...uniqueUserIds.map((userId: string) => {
      const timesheet = timesheets.find(ts => ts.userId === userId);
      return {
        key: userId,
        text: timesheet?.userName || userId
      };
    })
  ];

  const timesheetColumns: IColumn[] = [
    {
      key: 'user',
      name: 'User',
      fieldName: 'userName',
      minWidth: 120,
      maxWidth: 180,
      isResizable: true
    },
    {
      key: 'week',
      name: 'Week',
      minWidth: 120,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small">
          {DateUtils.formatWeekRange(item.weekStartDate, item.weekEndDate)}
        </Text>
      )
    },
    {
      key: 'hours',
      name: 'Total Hours',
      fieldName: 'totalHours',
      minWidth: 80,
      maxWidth: 100,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small">{item.totalHours.toFixed(2)}</Text>
      )
    },
    {
      key: 'status',
      name: 'Status',
      fieldName: 'status',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small" style={{ color: getStatusColor(item.status), fontWeight: 'bold' }}>
          {item.status}
        </Text>
      )
    },
    {
      key: 'submitted',
      name: 'Submitted',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: ITimesheet) => (
        <Text variant="small">
          {item.submittedDate ? DateUtils.formatDisplayDate(item.submittedDate) : '-'}
        </Text>
      )
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 150,
      maxWidth: 150,
      isResizable: false,
      onRender: (item: ITimesheet) => (
        <Stack horizontal tokens={{ childrenGap: 5 }}>
          <TooltipHost content="View Details">
            <IconButton
              iconProps={{ iconName: 'View' }}
              onClick={() => openTimesheetDetails(item)}
            />
          </TooltipHost>
          {canApproveReject(item) && (
            <>
              <TooltipHost content="Approve">
                <IconButton
                  iconProps={{ iconName: 'CheckMark' }}
                  onClick={() => openApprovalDialog(item)}
                  styles={{ root: { color: '#107c10' } }}
                />
              </TooltipHost>
              <TooltipHost content="Reject">
                <IconButton
                  iconProps={{ iconName: 'Cancel' }}
                  onClick={() => openRejectionDialog(item)}
                  styles={{ root: { color: '#a80000' } }}
                />
              </TooltipHost>
            </>
          )}
        </Stack>
      )
    }
  ];

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>Timesheet Approval</div>

      <CommandBar
        items={commandBarItems}
        className={styles.commandBar}
      />

      {/* Filters */}
      <div className={styles.filtersSection}>
        <Text variant="medium" style={{ fontWeight: 'bold', marginBottom: '15px' }}>
          Filters
        </Text>
        <Stack horizontal wrap tokens={{ childrenGap: 15 }}>
          <Stack.Item>
            <Dropdown
              label="Status"
              options={statusOptions}
              selectedKey={filters.status}
              onChange={(_, option) => setFilters({
                ...filters,
                status: option?.key as TimesheetStatus | 'all'
              })}
              styles={{ dropdown: { width: 150 } }}
            />
          </Stack.Item>
          <Stack.Item>
            <Dropdown
              label="User"
              options={userOptions}
              selectedKey={filters.userId}
              onChange={(_, option) => setFilters({
                ...filters,
                userId: option?.key as string
              })}
              styles={{ dropdown: { width: 200 } }}
            />
          </Stack.Item>
        </Stack>
      </div>

      {loading && (
        <div className={styles.loadingContainer}>
          <Spinner size={SpinnerSize.large} label="Loading timesheets..." />
        </div>
      )}

      {/* Timesheets List */}
      <DetailsList
        items={filteredTimesheets}
        columns={timesheetColumns}
        layoutMode={DetailsListLayoutMode.justified}
        selectionMode={SelectionMode.none}
        isHeaderVisible={true}
        className={styles.detailsList}
      />

      {filteredTimesheets.length === 0 && !loading && (
        <div className={styles.emptyState}>
          <div className={styles.emptyTitle}>No Timesheets Found</div>
          <div className={styles.emptyDescription}>
            No timesheets match the current filters. Try adjusting your filter criteria.
          </div>
        </div>
      )}

      {/* Approval Dialog */}
      <Dialog
        hidden={!showApprovalDialog}
        onDismiss={() => setShowApprovalDialog(false)}
        dialogContentProps={{
          title: 'Approve Timesheet',
          subText: selectedTimesheet ?
            `Are you sure you want to approve the timesheet for ${selectedTimesheet.userName} for the week of ${DateUtils.formatWeekRange(selectedTimesheet.weekStartDate, selectedTimesheet.weekEndDate)}?` :
            ''
        }}
        modalProps={{ isBlocking: true }}
      >
        <DialogFooter>
          <PrimaryButton
            onClick={() => selectedTimesheet && approveTimesheet(selectedTimesheet).catch(console.error)}
            text="Approve"
            disabled={loading}
          />
          <DefaultButton onClick={() => setShowApprovalDialog(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog
        hidden={!showRejectionDialog}
        onDismiss={() => setShowRejectionDialog(false)}
        dialogContentProps={{
          title: 'Reject Timesheet',
          subText: selectedTimesheet ?
            `Provide a reason for rejecting ${selectedTimesheet.userName}'s timesheet:` :
            ''
        }}
        modalProps={{ isBlocking: true }}
      >
        <DialogContent>
          <TextField
            label="Rejection Reason"
            value={rejectionReason}
            onChange={(_, value) => setRejectionReason(value || '')}
            multiline
            rows={4}
            placeholder="Please provide a clear reason for rejection..."
            required
          />
        </DialogContent>
        <DialogFooter>
          <PrimaryButton
            onClick={() => selectedTimesheet && rejectTimesheet(selectedTimesheet, rejectionReason).catch(console.error)}
            text="Reject"
            disabled={loading || !rejectionReason.trim()}
          />
          <DefaultButton onClick={() => setShowRejectionDialog(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Timesheet Details Panel */}
      <Panel
        isOpen={showTimesheetPanel}
        onDismiss={() => setShowTimesheetPanel(false)}
        type={PanelType.large}
        headerText={selectedTimesheet ? `Timesheet Details - ${selectedTimesheet.userName}` : ''}
        closeButtonAriaLabel="Close"
      >
        {selectedTimesheet && (
          <Stack tokens={{ childrenGap: 20 }}>
            <div>
              <Text variant="large" style={{ fontWeight: 'bold' }}>
                Week of {DateUtils.formatWeekRange(selectedTimesheet.weekStartDate, selectedTimesheet.weekEndDate)}
              </Text>
              <Text variant="medium" block style={{ marginTop: '5px' }}>
                Status: <span style={{ color: getStatusColor(selectedTimesheet.status), fontWeight: 'bold' }}>
                  {selectedTimesheet.status}
                </span>
              </Text>
            </div>

            {selectedTimesheet.timeEntries.length > 0 && (
              <CostSummary
                timeEntries={selectedTimesheet.timeEntries}
                projects={projects}
                title="Time Entry Summary"
                showBreakdown={true}
                showExport={false}
              />
            )}

            {selectedTimesheet.rejectionReason && (
              <div style={{
                padding: '15px',
                backgroundColor: '#fef7f7',
                border: '1px solid #a80000',
                borderRadius: '4px'
              }}>
                <Text variant="medium" style={{ fontWeight: 'bold', color: '#a80000' }}>
                  Rejection Reason:
                </Text>
                <Text variant="medium" block style={{ marginTop: '5px' }}>
                  {selectedTimesheet.rejectionReason}
                </Text>
              </div>
            )}

            {canApproveReject(selectedTimesheet) && (
              <Stack horizontal tokens={{ childrenGap: 10 }}>
                <PrimaryButton
                  text="Approve"
                  iconProps={{ iconName: 'CheckMark' }}
                  onClick={() => {
                    setShowTimesheetPanel(false);
                    openApprovalDialog(selectedTimesheet);
                  }}
                />
                <DefaultButton
                  text="Reject"
                  iconProps={{ iconName: 'Cancel' }}
                  onClick={() => {
                    setShowTimesheetPanel(false);
                    openRejectionDialog(selectedTimesheet);
                  }}
                />
              </Stack>
            )}
          </Stack>
        )}
      </Panel>
    </div>
  );
};

export default ApprovalView;