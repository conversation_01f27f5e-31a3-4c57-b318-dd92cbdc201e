import * as React from 'react';
import { useState } from 'react';
import {
  MessageBar,
  MessageBarType
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import styles from '../QtsTimesheetTracker.module.scss';

interface IApprovalViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
}

const ApprovalView: React.FC<IApprovalViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService
}) => {
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>Timesheet Approvals</div>

      <div className={styles.emptyState}>
        <div className={styles.emptyTitle}>Approval System Coming Soon</div>
        <div className={styles.emptyDescription}>
          Timesheet approval workflow including submission, review, approval, rejection, 
          and delegation features will be implemented in the next phase.
        </div>
        <div style={{ textAlign: 'left', marginTop: '20px' }}>
          <strong>Planned Approval Features:</strong>
          <ul style={{ marginTop: '10px' }}>
            <li>Pending Timesheets for Approval</li>
            <li>Approve/Reject Timesheets</li>
            <li>Bulk Approval Actions</li>
            <li>Approval History</li>
            <li>Delegation of Approval Rights</li>
            <li>Email Notifications</li>
            <li>Approval Comments and Feedback</li>
            <li>Post-Approval Editing (Admin/Program Manager)</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ApprovalView;