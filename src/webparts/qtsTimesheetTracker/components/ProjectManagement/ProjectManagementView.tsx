import * as React from 'react';
import { useState } from 'react';
import {
  MessageBar,
  MessageBarType,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Text,
  DetailsList,
  IColumn,
  DetailsListLayoutMode,
  SelectionMode
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject, ProjectStatus } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import styles from '../QtsTimesheetTracker.module.scss';

interface IProjectManagementViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
  onProjectsUpdated: (projects: IProject[]) => void;
}

const ProjectManagementView: React.FC<IProjectManagementViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService,
  onProjectsUpdated
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);
  const [selectedProject, setSelectedProject] = useState<IProject | null>(null);

  const refreshProjects = async (): Promise<void> => {
    try {
      setLoading(true);
      const updatedProjects = await sharePointService.getProjects();
      onProjectsUpdated(updatedProjects);
      setMessage({
        text: 'Projects refreshed successfully!',
        type: MessageBarType.success
      });
    } catch (error) {
      setMessage({
        text: 'Failed to refresh projects. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error refreshing projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const commandBarItems: ICommandBarItemProps[] = [
    {
      key: 'newProject',
      text: 'New Project',
      iconProps: { iconName: 'Add' },
      onClick: () => {
        // TODO: Open new project dialog
        setMessage({
          text: 'New project dialog will be implemented in the next phase.',
          type: MessageBarType.info
        });
      }
    },
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: (): void => { 
        refreshProjects().catch(console.error);
      },
      disabled: loading
    }
  ];

  const farCommandBarItems: ICommandBarItemProps[] = [
    {
      key: 'edit',
      text: 'Edit',
      iconProps: { iconName: 'Edit' },
      disabled: !selectedProject,
      onClick: () => {
        // TODO: Open edit project dialog
        setMessage({
          text: 'Edit project dialog will be implemented in the next phase.',
          type: MessageBarType.info
        });
      }
    },
    {
      key: 'delete',
      text: 'Delete',
      iconProps: { iconName: 'Delete' },
      disabled: !selectedProject,
      onClick: () => {
        // TODO: Confirm and delete project
        setMessage({
          text: 'Delete project functionality will be implemented in the next phase.',
          type: MessageBarType.info
        });
      }
    }
  ];

  const columns: IColumn[] = [
    {
      key: 'title',
      name: 'Project Name',
      fieldName: 'title',
      minWidth: 200,
      maxWidth: 300,
      isResizable: true,
      onRender: (item: IProject) => (
        <Text variant="medium" style={{ fontWeight: 600 }}>
          {item.title}
        </Text>
      )
    },
    {
      key: 'client',
      name: 'Client',
      fieldName: 'client',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'status',
      name: 'Status',
      fieldName: 'status',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: IProject) => (
        <Text
          variant="medium"
          style={{
            color: getStatusColor(item.status),
            fontWeight: 600
          }}
        >
          {item.status}
        </Text>
      )
    },
    {
      key: 'projectManager',
      name: 'Project Manager',
      fieldName: 'projectManager',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'startDate',
      name: 'Start Date',
      fieldName: 'startDate',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: IProject) => (
        <Text variant="medium">
          {item.startDate ? item.startDate.toLocaleDateString() : '-'}
        </Text>
      )
    },
    {
      key: 'endDate',
      name: 'End Date',
      fieldName: 'endDate',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: IProject) => (
        <Text variant="medium">
          {item.endDate ? item.endDate.toLocaleDateString() : '-'}
        </Text>
      )
    },
    {
      key: 'hourlyRate',
      name: 'Hourly Rate',
      fieldName: 'hourlyRate',
      minWidth: 100,
      maxWidth: 120,
      isResizable: true,
      onRender: (item: IProject) => (
        <Text variant="medium">
          {item.hourlyRate ? `$${item.hourlyRate.toFixed(2)}` : '-'}
        </Text>
      )
    }
  ];

  const getStatusColor = (status: ProjectStatus): string => {
    switch (status) {
      case ProjectStatus.Active:
        return '#107c10';
      case ProjectStatus.Completed:
        return '#0078d4';
      case ProjectStatus.OnHold:
        return '#d83b01';
      case ProjectStatus.Cancelled:
        return '#a80000';
      default:
        return '#605e5c';
    }
  };

  const onItemSelected = (item?: IProject): void => {
    setSelectedProject(item || null);
  };

  const getProjectSummary = (): { activeProjects: number; completedProjects: number; totalProjects: number } => {
    const activeProjects = projects.filter(p => p.status === ProjectStatus.Active).length;
    const completedProjects = projects.filter(p => p.status === ProjectStatus.Completed).length;
    const totalProjects = projects.length;

    return { activeProjects, completedProjects, totalProjects };
  };

  const summary = getProjectSummary();

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>Project Management</div>

      <CommandBar
        items={commandBarItems}
        farItems={farCommandBarItems}
        className={styles.commandBar}
      />

      <div className={styles.summaryCards}>
        <div className={styles.summaryCard}>
          <div className={styles.cardTitle}>Total Projects</div>
          <div className={styles.cardValue}>{summary.totalProjects}</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.cardTitle}>Active Projects</div>
          <div className={styles.cardValue}>{summary.activeProjects}</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.cardTitle}>Completed Projects</div>
          <div className={styles.cardValue}>{summary.completedProjects}</div>
        </div>
      </div>

      {projects.length === 0 ? (
        <div className={styles.emptyState}>
          <div className={styles.emptyTitle}>No Projects Found</div>
          <div className={styles.emptyDescription}>
            Get started by creating your first project to track time and manage activities.
          </div>
          <PrimaryButton 
            text="Create First Project" 
            onClick={() => {
              setMessage({
                text: 'Create project functionality will be implemented in the next phase.',
                type: MessageBarType.info
              });
            }}
          />
        </div>
      ) : (
        <DetailsList
          items={projects}
          columns={columns}
          setKey="set"
          layoutMode={DetailsListLayoutMode.justified}
          selectionMode={SelectionMode.single}
          onActiveItemChanged={onItemSelected}
          isHeaderVisible={true}
          className={styles.dataGrid}
        />
      )}

      {selectedProject && (
        <div style={{ marginTop: '20px', padding: '16px', backgroundColor: 'var(--neutralLighterAlt)', borderRadius: '4px' }}>
          <Text variant="large" style={{ fontWeight: 600, marginBottom: '8px', display: 'block' }}>
            Project Details
          </Text>
          <Text variant="medium" style={{ display: 'block', marginBottom: '4px' }}>
            <strong>Description:</strong> {selectedProject.description || 'No description available'}
          </Text>
          <Text variant="medium" style={{ display: 'block', marginBottom: '4px' }}>
            <strong>Company:</strong> {selectedProject.company || 'Not specified'}
          </Text>
          <Text variant="medium" style={{ display: 'block', marginBottom: '4px' }}>
            <strong>Team Members:</strong> {selectedProject.teamMembers?.length || 0} members
          </Text>
          <Text variant="medium" style={{ display: 'block' }}>
            <strong>Activities:</strong> {selectedProject.activities?.length || 0} activities
          </Text>
        </div>
      )}
    </div>
  );
};

export default ProjectManagementView;