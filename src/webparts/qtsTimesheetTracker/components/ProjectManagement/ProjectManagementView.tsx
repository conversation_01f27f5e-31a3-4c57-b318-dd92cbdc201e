import * as React from 'react';
import { useState, useRef } from 'react';
import {
  MessageBar,
  MessageBarType,
  PrimaryButton,
  DefaultButton,
  CommandBar,
  ICommandBarItemProps,
  Text,
  DetailsList,
  IColumn,
  DetailsListLayoutMode,
  SelectionMode,
  Panel,
  PanelType,
  TextField,
  Dropdown,
  <PERSON>ropdownOption,
  DatePicker,
  Stack,
  Dialog,
  DialogFooter,
  DialogContent,
  Spinner,
  SpinnerSize,
  IconButton,
  TooltipHost
} from '@fluentui/react';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IUser, IProject, ProjectStatus } from '../../models';
import { SharePointService } from '../../services/SharePointService';
import styles from '../QtsTimesheetTracker.module.scss';

interface IProjectManagementViewProps {
  context: WebPartContext;
  currentUser: IUser;
  projects: IProject[];
  sharePointService: SharePointService;
  onProjectsUpdated: (projects: IProject[]) => void;
}

const ProjectManagementView: React.FC<IProjectManagementViewProps> = ({
  context,
  currentUser,
  projects,
  sharePointService,
  onProjectsUpdated
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: MessageBarType } | null>(null);
  const [showProjectPanel, setShowProjectPanel] = useState<boolean>(false);
  const [showBulkUpload, setShowBulkUpload] = useState<boolean>(false);
  const [editingProject, setEditingProject] = useState<IProject | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const refreshProjects = async (): Promise<void> => {
    try {
      setLoading(true);
      const updatedProjects = await sharePointService.getProjects();
      onProjectsUpdated(updatedProjects);
      setMessage({
        text: 'Projects refreshed successfully!',
        type: MessageBarType.success
      });
    } catch (error) {
      setMessage({
        text: 'Failed to refresh projects. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error refreshing projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const openProjectPanel = (project?: IProject): void => {
    setEditingProject(project || {
      title: '',
      description: '',
      client: '',
      company: '',
      status: ProjectStatus.Active,
      hourlyRate: 0,
      onsiteRate: 0,
      projectManager: '',
      teamMembers: [],
      activities: [],
      customFields: {}
    });
    setShowProjectPanel(true);
  };

  const closeProjectPanel = (): void => {
    setShowProjectPanel(false);
    setEditingProject(null);
  };

  const saveProject = async (): Promise<void> => {
    if (!editingProject || !editingProject.title.trim()) {
      setMessage({
        text: 'Project title is required.',
        type: MessageBarType.error
      });
      return;
    }

    try {
      setLoading(true);
      let success = false;

      if (editingProject.id) {
        // Update existing project
        success = await sharePointService.updateProject(editingProject);
      } else {
        // Create new project
        const createdProject = await sharePointService.createProject(editingProject);
        success = !!createdProject;
      }

      if (success) {
        await refreshProjects();
        closeProjectPanel();
        setMessage({
          text: `Project ${editingProject.id ? 'updated' : 'created'} successfully!`,
          type: MessageBarType.success
        });
      } else {
        setMessage({
          text: `Failed to ${editingProject.id ? 'update' : 'create'} project. Please try again.`,
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error saving project. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error saving project:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteProject = async (project: IProject): Promise<void> => {
    if (!project.id) return;

    if (!confirm(`Are you sure you want to delete the project "${project.title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setLoading(true);
      const success = await sharePointService.deleteProject(project.id);

      if (success) {
        await refreshProjects();
        setMessage({
          text: 'Project deleted successfully!',
          type: MessageBarType.success
        });
      } else {
        setMessage({
          text: 'Failed to delete project. Please try again.',
          type: MessageBarType.error
        });
      }
    } catch (error) {
      setMessage({
        text: 'Error deleting project. Please try again.',
        type: MessageBarType.error
      });
      console.error('Error deleting project:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCsvUpload = (): void => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const processCsvFile = async (file: File): Promise<void> => {
    try {
      setLoading(true);
      const text = await file.text();
      const lines = text.split('\n').filter(line => line.trim());

      if (lines.length < 2) {
        setMessage({
          text: 'CSV file must contain at least a header row and one data row.',
          type: MessageBarType.error
        });
        return;
      }

      const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
      const requiredHeaders = ['title', 'description', 'client', 'status'];

      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
      if (missingHeaders.length > 0) {
        setMessage({
          text: `CSV file is missing required columns: ${missingHeaders.join(', ')}`,
          type: MessageBarType.error
        });
        return;
      }

      const projectsToCreate: IProject[] = [];

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim());
        const project: IProject = {
          title: values[headers.indexOf('title')] || '',
          description: values[headers.indexOf('description')] || '',
          client: values[headers.indexOf('client')] || '',
          company: values[headers.indexOf('company')] || '',
          status: (values[headers.indexOf('status')] as ProjectStatus) || ProjectStatus.Active,
          hourlyRate: parseFloat(values[headers.indexOf('hourlyrate')]) || 0,
          onsiteRate: parseFloat(values[headers.indexOf('onsiterate')]) || 0,
          projectManager: values[headers.indexOf('projectmanager')] || '',
          teamMembers: [],
          activities: [],
          customFields: {}
        };

        if (project.title) {
          projectsToCreate.push(project);
        }
      }

      let successCount = 0;
      for (const project of projectsToCreate) {
        const created = await sharePointService.createProject(project);
        if (created) successCount++;
      }

      await refreshProjects();
      setMessage({
        text: `Successfully created ${successCount} out of ${projectsToCreate.length} projects.`,
        type: successCount === projectsToCreate.length ? MessageBarType.success : MessageBarType.warning
      });

    } catch (error) {
      setMessage({
        text: 'Error processing CSV file. Please check the file format.',
        type: MessageBarType.error
      });
      console.error('Error processing CSV:', error);
    } finally {
      setLoading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const commandBarItems: ICommandBarItemProps[] = [
    {
      key: 'newProject',
      text: 'New Project',
      iconProps: { iconName: 'Add' },
      onClick: () => openProjectPanel()
    },
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => refreshProjects().catch(console.error)
    },
    {
      key: 'bulkUpload',
      text: 'Bulk Upload',
      iconProps: { iconName: 'Upload' },
      onClick: () => setShowBulkUpload(true)
    }
  ];

  const getStatusColor = (status: ProjectStatus): string => {
    switch (status) {
      case ProjectStatus.Active:
        return '#107c10';
      case ProjectStatus.Completed:
        return '#0078d4';
      case ProjectStatus.OnHold:
        return '#d83b01';
      case ProjectStatus.Cancelled:
        return '#a80000';
      default:
        return '#605e5c';
    }
  };

  const projectColumns: IColumn[] = [
    {
      key: 'title',
      name: 'Project Name',
      fieldName: 'title',
      minWidth: 150,
      maxWidth: 250,
      isResizable: true,
      onRender: (item: IProject) => (
        <div>
          <Text variant="medium" block style={{ fontWeight: 'bold' }}>
            {item.title}
          </Text>
          {item.client && (
            <Text variant="small" style={{ color: '#605e5c' }}>
              Client: {item.client}
            </Text>
          )}
        </div>
      )
    },
    {
      key: 'status',
      name: 'Status',
      fieldName: 'status',
      minWidth: 80,
      maxWidth: 100,
      isResizable: true,
      onRender: (item: IProject) => (
        <Text
          variant="small"
          style={{
            color: getStatusColor(item.status),
            fontWeight: 'bold'
          }}
        >
          {item.status}
        </Text>
      )
    },
    {
      key: 'projectManager',
      name: 'Project Manager',
      fieldName: 'projectManager',
      minWidth: 120,
      maxWidth: 180,
      isResizable: true
    },
    {
      key: 'teamMembers',
      name: 'Team Size',
      minWidth: 80,
      maxWidth: 100,
      isResizable: true,
      onRender: (item: IProject) => (
        <Text variant="small">
          {item.teamMembers?.length || 0} members
        </Text>
      )
    },
    {
      key: 'rates',
      name: 'Rates',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: IProject) => (
        <div>
          {item.hourlyRate && (
            <Text variant="small" block>
              Office: ${item.hourlyRate}/hr
            </Text>
          )}
          {item.onsiteRate && (
            <Text variant="small" block>
              Onsite: ${item.onsiteRate}/hr
            </Text>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 120,
      maxWidth: 120,
      isResizable: false,
      onRender: (item: IProject) => (
        <Stack horizontal tokens={{ childrenGap: 5 }}>
          <TooltipHost content="Edit Project">
            <IconButton
              iconProps={{ iconName: 'Edit' }}
              onClick={() => openProjectPanel(item)}
            />
          </TooltipHost>
          <TooltipHost content="Manage Activities">
            <IconButton
              iconProps={{ iconName: 'TaskManager' }}
              onClick={() => {
                setMessage({
                  text: 'Activity management will be implemented in the next phase.',
                  type: MessageBarType.info
                });
              }}
            />
          </TooltipHost>
          <TooltipHost content="Delete Project">
            <IconButton
              iconProps={{ iconName: 'Delete' }}
              onClick={() => deleteProject(item)}
              styles={{ root: { color: '#a80000' } }}
            />
          </TooltipHost>
        </Stack>
      )
    }
  ];

  const statusOptions: IDropdownOption[] = [
    { key: ProjectStatus.Active, text: 'Active' },
    { key: ProjectStatus.Completed, text: 'Completed' },
    { key: ProjectStatus.OnHold, text: 'On Hold' },
    { key: ProjectStatus.Cancelled, text: 'Cancelled' }
  ];

  const renderProjectForm = (): JSX.Element => (
    <Panel
      isOpen={showProjectPanel}
      onDismiss={closeProjectPanel}
      type={PanelType.medium}
      headerText={editingProject?.id ? 'Edit Project' : 'New Project'}
      closeButtonAriaLabel="Close"
    >
      {editingProject && (
        <Stack tokens={{ childrenGap: 20 }}>
          <TextField
            label="Project Name"
            value={editingProject.title}
            onChange={(_, value) => setEditingProject({
              ...editingProject,
              title: value || ''
            })}
            required
          />

          <TextField
            label="Description"
            value={editingProject.description}
            onChange={(_, value) => setEditingProject({
              ...editingProject,
              description: value || ''
            })}
            multiline
            rows={3}
          />

          <Stack horizontal tokens={{ childrenGap: 15 }}>
            <Stack.Item grow>
              <TextField
                label="Client"
                value={editingProject.client}
                onChange={(_, value) => setEditingProject({
                  ...editingProject,
                  client: value || ''
                })}
              />
            </Stack.Item>
            <Stack.Item grow>
              <TextField
                label="Company"
                value={editingProject.company}
                onChange={(_, value) => setEditingProject({
                  ...editingProject,
                  company: value || ''
                })}
              />
            </Stack.Item>
          </Stack>

          <Dropdown
            label="Status"
            options={statusOptions}
            selectedKey={editingProject.status}
            onChange={(_, option) => setEditingProject({
              ...editingProject,
              status: option?.key as ProjectStatus
            })}
            required
          />

          <Stack horizontal tokens={{ childrenGap: 15 }}>
            <Stack.Item grow>
              <DatePicker
                label="Start Date"
                value={editingProject.startDate}
                onSelectDate={(date) => setEditingProject({
                  ...editingProject,
                  startDate: date || undefined
                })}
                placeholder="Select start date"
              />
            </Stack.Item>
            <Stack.Item grow>
              <DatePicker
                label="End Date"
                value={editingProject.endDate}
                onSelectDate={(date) => setEditingProject({
                  ...editingProject,
                  endDate: date || undefined
                })}
                placeholder="Select end date"
              />
            </Stack.Item>
          </Stack>

          <Stack horizontal tokens={{ childrenGap: 15 }}>
            <Stack.Item grow>
              <TextField
                label="Hourly Rate ($)"
                value={editingProject.hourlyRate?.toString() || ''}
                onChange={(_, value) => setEditingProject({
                  ...editingProject,
                  hourlyRate: parseFloat(value || '0') || 0
                })}
                type="number"
                min="0"
                step="0.01"
              />
            </Stack.Item>
            <Stack.Item grow>
              <TextField
                label="Onsite Rate ($)"
                value={editingProject.onsiteRate?.toString() || ''}
                onChange={(_, value) => setEditingProject({
                  ...editingProject,
                  onsiteRate: parseFloat(value || '0') || 0
                })}
                type="number"
                min="0"
                step="0.01"
              />
            </Stack.Item>
          </Stack>

          <TextField
            label="Project Manager"
            value={editingProject.projectManager}
            onChange={(_, value) => setEditingProject({
              ...editingProject,
              projectManager: value || ''
            })}
            placeholder="Enter project manager name"
          />

          <Stack horizontal tokens={{ childrenGap: 10 }} horizontalAlign="end">
            <PrimaryButton
              text="Save"
              onClick={() => saveProject().catch(console.error)}
              disabled={loading || !editingProject.title.trim()}
            />
            <DefaultButton
              text="Cancel"
              onClick={closeProjectPanel}
            />
          </Stack>
        </Stack>
      )}
    </Panel>
  );

  const renderBulkUploadDialog = (): JSX.Element => (
    <Dialog
      hidden={!showBulkUpload}
      onDismiss={() => setShowBulkUpload(false)}
      dialogContentProps={{
        title: 'Bulk Upload Projects',
        subText: 'Upload a CSV file to create multiple projects at once.'
      }}
      modalProps={{ isBlocking: true }}
    >
      <DialogContent>
        <Stack tokens={{ childrenGap: 15 }}>
          <Text variant="medium">
            CSV file should contain the following columns:
          </Text>
          <Text variant="small" style={{ fontFamily: 'monospace', backgroundColor: '#f3f2f1', padding: '10px' }}>
            title,description,client,company,status,hourlyrate,onsiterate,projectmanager
          </Text>
          <Text variant="small">
            <strong>Required columns:</strong> title, description, client, status<br/>
            <strong>Status values:</strong> Active, Completed, OnHold, Cancelled
          </Text>

          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            style={{ display: 'none' }}
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                processCsvFile(file).catch(console.error);
                setShowBulkUpload(false);
              }
            }}
          />

          <PrimaryButton
            text="Select CSV File"
            iconProps={{ iconName: 'Upload' }}
            onClick={handleCsvUpload}
          />
        </Stack>
      </DialogContent>
      <DialogFooter>
        <DefaultButton onClick={() => setShowBulkUpload(false)} text="Cancel" />
      </DialogFooter>
    </Dialog>
  );

  return (
    <div className={styles.viewContainer}>
      {message && (
        <MessageBar
          messageBarType={message.type}
          onDismiss={() => setMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {message.text}
        </MessageBar>
      )}

      <div className={styles.sectionTitle}>Project Management</div>

      <CommandBar
        items={commandBarItems}
        className={styles.commandBar}
      />

      {loading && (
        <div className={styles.loadingContainer}>
          <Spinner size={SpinnerSize.large} label="Loading projects..." />
        </div>
      )}

      <DetailsList
        items={projects}
        columns={projectColumns}
        layoutMode={DetailsListLayoutMode.justified}
        selectionMode={SelectionMode.none}
        isHeaderVisible={true}
        className={styles.detailsList}
      />

      {projects.length === 0 && !loading && (
        <div className={styles.emptyState}>
          <div className={styles.emptyTitle}>No Projects Found</div>
          <div className={styles.emptyDescription}>
            Get started by creating your first project or uploading projects from a CSV file.
          </div>
          <Stack horizontal tokens={{ childrenGap: 10 }} horizontalAlign="center">
            <PrimaryButton
              text="Create Project"
              iconProps={{ iconName: 'Add' }}
              onClick={() => openProjectPanel()}
            />
            <DefaultButton
              text="Bulk Upload"
              iconProps={{ iconName: 'Upload' }}
              onClick={() => setShowBulkUpload(true)}
            />
          </Stack>
        </div>
      )}

      {renderProjectForm()}
      {renderBulkUploadDialog()}
    </div>
  );
};

export default ProjectManagementView;