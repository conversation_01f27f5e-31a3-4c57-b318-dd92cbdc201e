import * as React from 'react';
import { useState, useEffect } from 'react';
import { 
  Pivot, 
  PivotItem, 
  MessageBar, 
  MessageBarType,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import styles from './QtsTimesheetTracker.module.scss';
import type { IQtsTimesheetTrackerProps } from './IQtsTimesheetTrackerProps';
import { SharePointService } from '../services/SharePointService';
import { IUser, IProject, UserRole } from '../models';
import TimesheetView from './TimeTracking/TimesheetView';
import ProjectManagementView from './ProjectManagement/ProjectManagementView';
import ReportingView from './Reporting/ReportingView';
import ApprovalView from './Approval/ApprovalView';
import UserManagementView from './UserManagement/UserManagementView';

const QtsTimesheetTracker: React.FC<IQtsTimesheetTrackerProps> = (props) => {
  const [currentUser, setCurrentUser] = useState<IUser | null>(null);
  const [projects, setProjects] = useState<IProject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [selectedTab, setSelectedTab] = useState<string>('timesheet');

  const sharePointService = new SharePointService(props.context);

  const initializeData = async (): Promise<void> => {
    try {
      setLoading(true);
      
      // Load current user
      const user = await sharePointService.getCurrentUser();
      if (user) {
        setCurrentUser(user);
      }

      // Load projects
      const projectsData = await sharePointService.getProjects();
      setProjects(projectsData);

    } catch (err) {
      setError('Failed to load application data. Please refresh the page and try again.');
      console.error('Error initializing data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    void initializeData();
  }, []);

  const handleTabChange = (item?: PivotItem): void => {
    if (item?.props.itemKey) {
      setSelectedTab(item.props.itemKey);
    }
  };

  const canAccessTab = (tabKey: string): boolean => {
    if (!currentUser) return false;

    switch (tabKey) {
      case 'timesheet':
        return true; // All users can access timesheet
      case 'projects':
        return [UserRole.Administrator, UserRole.ProgramManager, UserRole.ProjectManager].indexOf(currentUser.role) !== -1;
      case 'reports':
        return [UserRole.Administrator, UserRole.ProgramManager, UserRole.ProjectManager, UserRole.ProjectObserver].indexOf(currentUser.role) !== -1;
      case 'approval':
        return [UserRole.Administrator, UserRole.ProgramManager, UserRole.ProjectManager].indexOf(currentUser.role) !== -1;
      case 'users':
        return [UserRole.Administrator, UserRole.ProgramManager].indexOf(currentUser.role) !== -1;
      default:
        return false;
    }
  };

  if (loading) {
    return (
      <div className={styles.qtsTimesheetTracker}>
        <div className={styles.loadingContainer}>
          <Spinner size={SpinnerSize.large} label="Loading Timesheet 365..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.qtsTimesheetTracker}>
        <MessageBar messageBarType={MessageBarType.error}>
          {error}
        </MessageBar>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className={styles.qtsTimesheetTracker}>
        <MessageBar messageBarType={MessageBarType.warning}>
          Unable to load user information. Please refresh the page and try again.
        </MessageBar>
      </div>
    );
  }

  return (
    <div className={`${styles.qtsTimesheetTracker} ${props.hasTeamsContext ? styles.teams : ''}`}>
      <div className={styles.header}>
        <h1 className={styles.title}>Timesheet 365</h1>
        <div className={styles.userInfo}>
          Welcome, {currentUser.displayName} ({currentUser.role})
        </div>
      </div>

      <Pivot
        selectedKey={selectedTab}
        onLinkClick={handleTabChange}
        headersOnly={true}
        className={styles.pivot}
      >
        <PivotItem 
          headerText="My Timesheet" 
          itemKey="timesheet"
          className={styles.pivotItem}
        >
          <TimesheetView
            context={props.context}
            currentUser={currentUser}
            projects={projects}
            sharePointService={sharePointService}
          />
        </PivotItem>

        {canAccessTab('projects') && (
          <PivotItem 
            headerText="Project Management" 
            itemKey="projects"
            className={styles.pivotItem}
          >
            <ProjectManagementView
              context={props.context}
              currentUser={currentUser}
              projects={projects}
              sharePointService={sharePointService}
              onProjectsUpdated={setProjects}
            />
          </PivotItem>
        )}

        {canAccessTab('reports') && (
          <PivotItem 
            headerText="Reports" 
            itemKey="reports"
            className={styles.pivotItem}
          >
            <ReportingView
              context={props.context}
              currentUser={currentUser}
              projects={projects}
              sharePointService={sharePointService}
            />
          </PivotItem>
        )}

        {canAccessTab('approval') && (
          <PivotItem
            headerText="Approvals"
            itemKey="approval"
            className={styles.pivotItem}
          >
            <ApprovalView
              context={props.context}
              currentUser={currentUser}
              projects={projects}
              sharePointService={sharePointService}
            />
          </PivotItem>
        )}

        {canAccessTab('users') && (
          <PivotItem
            headerText="Users"
            itemKey="users"
            className={styles.pivotItem}
          >
            <UserManagementView
              context={props.context}
              currentUser={currentUser}
              sharePointService={sharePointService}
            />
          </PivotItem>
        )}
      </Pivot>
    </div>
  );
};

export default QtsTimesheetTracker;
