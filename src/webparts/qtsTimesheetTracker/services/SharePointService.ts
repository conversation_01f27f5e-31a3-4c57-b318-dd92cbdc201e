import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { IProject, ITimesheet, IUser, ProjectStatus, TimesheetStatus, UserRole } from '../models';

export class SharePointService {
  private spHttpClient: SPHttpClient;
  private siteUrl: string;
  // private _context: WebPartContext;

  constructor(context: WebPartContext) {
    // this._context = context;
    this.spHttpClient = context.spHttpClient;
    this.siteUrl = context.pageContext.web.absoluteUrl;
  }

  // List Names
  private readonly PROJECTS_LIST = 'Projects';
  private readonly TIMESHEETS_LIST = 'Timesheets';
  private readonly USERS_LIST = 'UserProfiles';

  // Projects
  public async getProjects(): Promise<IProject[]> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.PROJECTS_LIST}')/items`;
      const response: SPHttpClientResponse = await this.spHttpClient.get(
        endpoint,
        SPHttpClient.configurations.v1
      );
      
      if (response.ok) {
        const data = await response.json();
        return data.value.map(this.mapToProject);
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching projects:', error);
      return [];
    }
  }

  public async createProject(project: IProject): Promise<IProject | undefined> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.PROJECTS_LIST}')/items`;
      const body = JSON.stringify(this.mapProjectToListItem(project));
      
      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': ''
          },
          body: body
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        return this.mapToProject(data);
      }
      
      return undefined;
    } catch (error) {
      console.error('Error creating project:', error);
      return undefined;
    }
  }

  public async updateProject(project: IProject): Promise<boolean> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.PROJECTS_LIST}')/items(${project.id})`;
      const body = JSON.stringify(this.mapProjectToListItem(project));

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': '',
            'X-HTTP-Method': 'MERGE',
            'IF-MATCH': '*'
          },
          body: body
        }
      );

      return response.ok;
    } catch (error) {
      console.error('Error updating project:', error);
      return false;
    }
  }

  public async deleteProject(projectId: number): Promise<boolean> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.PROJECTS_LIST}')/items(${projectId})`;

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': '',
            'X-HTTP-Method': 'DELETE',
            'IF-MATCH': '*'
          }
        }
      );

      return response.ok;
    } catch (error) {
      console.error('Error deleting project:', error);
      return false;
    }
  }

  public async getUsers(): Promise<IUser[]> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.USERS_LIST}')/items`;

      const response: SPHttpClientResponse = await this.spHttpClient.get(
        endpoint,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        return data.value.map((item: any) => this.mapListItemToUser(item));
      }

      return [];
    } catch (error) {
      console.error('Error fetching users:', error);
      return [];
    }
  }

  // User management methods
  public async updateUser(user: IUser): Promise<boolean> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.USERS_LIST}')/items(${user.id})`;
      const body = JSON.stringify(this.mapUserToListItem(user));

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': '',
            'X-HTTP-Method': 'MERGE',
            'IF-MATCH': '*'
          },
          body: body
        }
      );

      return response.ok;
    } catch (error) {
      console.error('Error updating user:', error);
      return false;
    }
  }

  public async createUser(user: IUser): Promise<IUser | undefined> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.USERS_LIST}')/items`;
      const body = JSON.stringify(this.mapUserToListItem(user));

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': ''
          },
          body: body
        }
      );

      if (response.ok) {
        const data = await response.json();
        return this.mapListItemToUser(data);
      }

      return undefined;
    } catch (error) {
      console.error('Error creating user:', error);
      return undefined;
    }
  }

  public async deleteUser(userId: string): Promise<boolean> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.USERS_LIST}')/items(${userId})`;

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': '',
            'X-HTTP-Method': 'DELETE',
            'IF-MATCH': '*'
          }
        }
      );

      return response.ok;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }

  private mapUserToListItem(user: IUser): any {
    return {
      Title: user.displayName,
      UserId: user.id,
      Email: user.email,
      Role: user.role,
      HourlyRate: user.hourlyRate,
      OnsiteRate: user.onsiteRate,
      MaxDailyHours: user.maxDailyHours,
      AllowWeekends: user.allowWeekends,
      Manager: user.manager,
      Department: user.department,
      IsActive: user.isActive
    };
  }

  private mapListItemToUser(item: any): IUser {
    return {
      id: item.Id?.toString() || item.ID?.toString() || '',
      displayName: item.Title || '',
      email: item.Email || '',
      role: item.Role || UserRole.User,
      hourlyRate: item.HourlyRate || 0,
      onsiteRate: item.OnsiteRate || 0,
      maxDailyHours: item.MaxDailyHours || 8,
      allowWeekends: item.AllowWeekends || false,
      manager: item.Manager || '',
      department: item.Department || '',
      isActive: item.IsActive !== false,
      created: item.Created ? new Date(item.Created) : undefined,
      modified: item.Modified ? new Date(item.Modified) : undefined
    };
  }

  // Timesheets
  public async getTimesheets(userId?: string, weekStartDate?: Date): Promise<ITimesheet[]> {
    try {
      let filter = '';
      if (userId) {
        filter += `UserId eq '${userId}'`;
      }
      if (weekStartDate) {
        if (filter) filter += ' and ';
        filter += `WeekStartDate eq '${weekStartDate.toISOString()}'`;
      }
      
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.TIMESHEETS_LIST}')/items${filter ? `?$filter=${filter}` : ''}`;
      const response: SPHttpClientResponse = await this.spHttpClient.get(
        endpoint,
        SPHttpClient.configurations.v1
      );
      
      if (response.ok) {
        const data = await response.json();
        return data.value.map(this.mapToTimesheet);
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching timesheets:', error);
      return [];
    }
  }

  public async createTimesheet(timesheet: ITimesheet): Promise<ITimesheet | undefined> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.TIMESHEETS_LIST}')/items`;
      const body = JSON.stringify(this.mapTimesheetToListItem(timesheet));
      
      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': ''
          },
          body: body
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        return this.mapToTimesheet(data);
      }
      
      return undefined;
    } catch (error) {
      console.error('Error creating timesheet:', error);
      return undefined;
    }
  }

  public async updateTimesheet(timesheet: ITimesheet): Promise<boolean> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/lists/getbytitle('${this.TIMESHEETS_LIST}')/items(${timesheet.id})`;
      const body = JSON.stringify(this.mapTimesheetToListItem(timesheet));
      
      const response: SPHttpClientResponse = await this.spHttpClient.post(
        endpoint,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-type': 'application/json;odata=nometadata',
            'odata-version': '',
            'X-HTTP-Method': 'MERGE',
            'IF-MATCH': '*'
          },
          body: body
        }
      );
      
      return response.ok;
    } catch (error) {
      console.error('Error updating timesheet:', error);
      return false;
    }
  }

  // Current User
  public async getCurrentUser(): Promise<IUser | undefined> {
    try {
      const endpoint = `${this.siteUrl}/_api/web/currentuser`;
      const response: SPHttpClientResponse = await this.spHttpClient.get(
        endpoint,
        SPHttpClient.configurations.v1
      );
      
      if (response.ok) {
        const data = await response.json();
        return this.mapToUser(data);
      }
      
      return undefined;
    } catch (error) {
      console.error('Error fetching current user:', error);
      return undefined;
    }
  }

  // Helper methods to map SharePoint list items to our models
  private mapToProject(item: any): IProject {
    return {
      id: item.Id,
      title: item.Title,
      description: item.Description,
      client: item.Client,
      company: item.Company,
      startDate: item.StartDate ? new Date(item.StartDate) : undefined,
      endDate: item.EndDate ? new Date(item.EndDate) : undefined,
      status: item.Status as ProjectStatus,
      hourlyRate: item.HourlyRate,
      onsiteRate: item.OnsiteRate,
      projectManager: item.ProjectManager,
      teamMembers: item.TeamMembers ? JSON.parse(item.TeamMembers) : [],
      customFields: item.CustomFields ? JSON.parse(item.CustomFields) : {},
      created: new Date(item.Created),
      modified: new Date(item.Modified),
      createdBy: item.Author?.Title,
      modifiedBy: item.Editor?.Title
    };
  }

  private mapProjectToListItem(project: IProject): any {
    return {
      Title: project.title,
      Description: project.description,
      Client: project.client,
      Company: project.company,
      StartDate: project.startDate?.toISOString(),
      EndDate: project.endDate?.toISOString(),
      Status: project.status,
      HourlyRate: project.hourlyRate,
      OnsiteRate: project.onsiteRate,
      ProjectManager: project.projectManager,
      TeamMembers: project.teamMembers ? JSON.stringify(project.teamMembers) : null,
      CustomFields: project.customFields ? JSON.stringify(project.customFields) : null
    };
  }

  private mapToTimesheet(item: any): ITimesheet {
    return {
      id: item.Id,
      userId: item.UserId,
      userName: item.UserName,
      weekStartDate: new Date(item.WeekStartDate),
      weekEndDate: new Date(item.WeekEndDate),
      timeEntries: [], // Will be loaded separately
      totalHours: item.TotalHours,
      status: item.Status as TimesheetStatus,
      submittedDate: item.SubmittedDate ? new Date(item.SubmittedDate) : undefined,
      approvedDate: item.ApprovedDate ? new Date(item.ApprovedDate) : undefined,
      approvedBy: item.ApprovedBy,
      rejectedDate: item.RejectedDate ? new Date(item.RejectedDate) : undefined,
      rejectedBy: item.RejectedBy,
      rejectionReason: item.RejectionReason,
      comments: item.Comments,
      created: new Date(item.Created),
      modified: new Date(item.Modified)
    };
  }

  private mapTimesheetToListItem(timesheet: ITimesheet): any {
    return {
      UserId: timesheet.userId,
      UserName: timesheet.userName,
      WeekStartDate: timesheet.weekStartDate.toISOString(),
      WeekEndDate: timesheet.weekEndDate.toISOString(),
      TotalHours: timesheet.totalHours,
      Status: timesheet.status,
      SubmittedDate: timesheet.submittedDate?.toISOString(),
      ApprovedDate: timesheet.approvedDate?.toISOString(),
      ApprovedBy: timesheet.approvedBy,
      RejectedDate: timesheet.rejectedDate?.toISOString(),
      RejectedBy: timesheet.rejectedBy,
      RejectionReason: timesheet.rejectionReason,
      Comments: timesheet.comments
    };
  }

  private mapToUser(item: any): IUser {
    return {
      id: item.Id,
      displayName: item.Title,
      email: item.Email,
      title: item.JobTitle,
      role: UserRole.User, // Default role, should be managed separately
      isActive: true,
      created: new Date()
    };
  }
}