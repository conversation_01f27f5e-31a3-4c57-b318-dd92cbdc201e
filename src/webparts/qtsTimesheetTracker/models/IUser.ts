export interface IUser {
  id: string;
  displayName: string;
  email: string;
  title?: string;
  department?: string;
  role: UserRole;
  hourlyRate?: number;
  onsiteRate?: number;
  managerId?: string;
  managerName?: string;
  isActive: boolean;
  created?: Date;
  modified?: Date;
}

export enum UserRole {
  Administrator = "Administrator",
  ProgramManager = "ProgramManager",
  ProjectManager = "ProjectManager",
  Coordinator = "Coordinator",
  ProjectObserver = "ProjectObserver",
  User = "User"
}

export interface IUserPermissions {
  canCreateProjects: boolean;
  canManageProjects: boolean;
  canViewAllProjects: boolean;
  canApproveTimesheets: boolean;
  canViewAllTimesheets: boolean;
  canEditApprovedTimesheets: boolean;
  canManageUsers: boolean;
  canViewReports: boolean;
  canExportData: boolean;
}

export interface IUserProfile {
  user: IUser;
  permissions: IUserPermissions;
  assignedProjects: number[];
  managedProjects: number[];
}