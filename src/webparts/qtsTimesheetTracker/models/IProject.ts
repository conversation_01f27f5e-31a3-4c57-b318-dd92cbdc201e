export interface IProject {
  id?: number;
  title: string;
  description?: string;
  client?: string;
  company?: string;
  startDate?: Date;
  endDate?: Date;
  status: ProjectStatus;
  hourlyRate?: number;
  onsiteRate?: number;
  projectManager?: string;
  teamMembers?: string[];
  activities?: IActivity[];
  customFields?: { [key: string]: unknown };
  created?: Date;
  modified?: Date;
  createdBy?: string;
  modifiedBy?: string;
}

export interface IActivity {
  id?: number;
  projectId: number;
  title: string;
  description?: string;
  estimatedHours?: number;
  actualHours?: number;
  status: ActivityStatus;
  assignedTo?: string[];
  created?: Date;
  modified?: Date;
}

export enum ProjectStatus {
  Active = "Active",
  Completed = "Completed",
  OnHold = "OnHold",
  Cancelled = "Cancelled"
}

export enum ActivityStatus {
  NotStarted = "NotStarted",
  InProgress = "InProgress",
  Completed = "Completed",
  OnHold = "OnHold"
}