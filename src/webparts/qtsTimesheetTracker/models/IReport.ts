export interface IReport {
  id?: number;
  title: string;
  description?: string;
  type: ReportType;
  filters: IReportFilter[];
  columns: IReportColumn[];
  data: unknown[];
  totalHours?: number;
  totalCost?: number;
  generatedDate: Date;
  generatedBy: string;
  parameters?: { [key: string]: unknown };
}

export interface IReportFilter {
  field: string;
  operator: FilterOperator;
  value: unknown;
  displayName: string;
}

export interface IReportColumn {
  key: string;
  name: string;
  fieldName: string;
  minWidth: number;
  maxWidth?: number;
  isResizable: boolean;
  isSorted?: boolean;
  isSortedDescending?: boolean;
  sortAscendingAriaLabel?: string;
  sortDescendingAriaLabel?: string;
  data?: unknown;
}

export enum ReportType {
  Daily = "Daily",
  Weekly = "Weekly",
  Monthly = "Monthly",
  Project = "Project",
  User = "User",
  Team = "Team",
  Custom = "Custom"
}

export enum FilterOperator {
  Equals = "Equals",
  NotEquals = "NotEquals",
  GreaterThan = "GreaterThan",
  LessThan = "LessThan",
  GreaterThanOrEqual = "GreaterThanOrEqual",
  LessThanOrEqual = "LessThanOrEqual",
  Contains = "Contains",
  StartsWith = "StartsWith",
  EndsWith = "EndsWith",
  In = "In",
  NotIn = "NotIn"
}

export interface IReportRequest {
  reportType: ReportType;
  startDate: Date;
  endDate: Date;
  projectIds?: number[];
  userIds?: string[];
  includeSubordinates?: boolean;
  groupBy?: string;
  sortBy?: string;
  sortDescending?: boolean;
  customFilters?: IReportFilter[];
}