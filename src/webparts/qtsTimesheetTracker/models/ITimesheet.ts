export interface ITimesheet {
  id?: number;
  userId: string;
  userName: string;
  weekStartDate: Date;
  weekEndDate: Date;
  timeEntries: ITimeEntry[];
  totalHours: number;
  status: TimesheetStatus;
  submittedDate?: Date;
  approvedDate?: Date;
  approvedBy?: string;
  rejectedDate?: Date;
  rejectedBy?: string;
  rejectionReason?: string;
  comments?: string;
  created?: Date;
  modified?: Date;
}

export interface ITimeEntry {
  id?: number;
  timesheetId?: number;
  projectId: number;
  projectTitle: string;
  activityId?: number;
  activityTitle?: string;
  date: Date;
  hours: number;
  workType: WorkType;
  description?: string;
  startTime?: Date;
  endTime?: Date;
  isOvertime?: boolean;
  created?: Date;
  modified?: Date;
}

export enum TimesheetStatus {
  Draft = "Draft",
  Submitted = "Submitted",
  Approved = "Approved",
  Rejected = "Rejected"
}

export enum WorkType {
  Office = "Office",
  Onsite = "Onsite",
  WFH = "WFH"
}

export interface ITimesheetSummary {
  userId: string;
  userName: string;
  weekStartDate: Date;
  totalHours: number;
  status: TimesheetStatus;
  submittedDate?: Date;
  approvedDate?: Date;
}