# SharePoint Lists Setup Guide

This document provides detailed instructions for setting up the required SharePoint lists for the Timesheet 365 application.

## Required Lists

### 1. Projects List

**List Name:** `Projects`
**Description:** Stores project information and configuration

#### Columns:
| Column Name | Type | Required | Description |
|-------------|------|----------|-------------|
| Title | Single line of text | Yes | Project name |
| Description | Multiple lines of text | No | Project description |
| Client | Single line of text | No | Client name |
| Company | Single line of text | No | Company name |
| StartDate | Date and Time | No | Project start date |
| EndDate | Date and Time | No | Project end date |
| Status | Choice | Yes | Project status (Active, Completed, OnHold, Cancelled) |
| HourlyRate | Number | No | Default hourly rate for the project |
| OnsiteRate | Number | No | Onsite hourly rate |
| ProjectManager | Person or Group | No | Project manager |
| TeamMembers | Person or Group (Multiple) | No | Team members assigned to project |
| CustomFields | Multiple lines of text | No | JSON string for custom fields |

#### Choice Values for Status:
- Active
- Completed
- OnHold
- Cancelled

### 2. Timesheets List

**List Name:** `Timesheets`
**Description:** Stores weekly timesheet records

#### Columns:
| Column Name | Type | Required | Description |
|-------------|------|----------|-------------|
| Title | Single line of text | Yes | Auto-generated (Week of [Date]) |
| UserId | Single line of text | Yes | User ID |
| UserName | Single line of text | Yes | User display name |
| WeekStartDate | Date and Time | Yes | Start date of the week |
| WeekEndDate | Date and Time | Yes | End date of the week |
| TimeEntries | Multiple lines of text | Yes | JSON string of time entries |
| TotalHours | Number | Yes | Total hours for the week |
| Status | Choice | Yes | Timesheet status |
| SubmittedDate | Date and Time | No | Date when submitted |
| ApprovedDate | Date and Time | No | Date when approved |
| ApprovedBy | Person or Group | No | Who approved the timesheet |
| RejectedDate | Date and Time | No | Date when rejected |
| RejectedBy | Person or Group | No | Who rejected the timesheet |
| RejectionReason | Multiple lines of text | No | Reason for rejection |
| Comments | Multiple lines of text | No | Additional comments |

#### Choice Values for Status:
- Draft
- Submitted
- Approved
- Rejected

### 3. Activities List

**List Name:** `Activities`
**Description:** Stores project activities/tasks

#### Columns:
| Column Name | Type | Required | Description |
|-------------|------|----------|-------------|
| Title | Single line of text | Yes | Activity name |
| ProjectId | Number | Yes | Related project ID |
| Description | Multiple lines of text | No | Activity description |
| EstimatedHours | Number | No | Estimated hours |
| ActualHours | Number | No | Actual hours spent |
| Status | Choice | Yes | Activity status |
| AssignedTo | Person or Group (Multiple) | No | Assigned team members |

#### Choice Values for Status:
- NotStarted
- InProgress
- Completed
- OnHold

### 4. UserProfiles List

**List Name:** `UserProfiles`
**Description:** Stores user-specific settings and roles

#### Columns:
| Column Name | Type | Required | Description |
|-------------|------|----------|-------------|
| Title | Single line of text | Yes | User display name |
| UserId | Single line of text | Yes | User ID |
| Email | Single line of text | Yes | User email |
| Role | Choice | Yes | User role in the system |
| HourlyRate | Number | No | User-specific hourly rate |
| OnsiteRate | Number | No | User-specific onsite rate |
| MaxDailyHours | Number | No | Maximum daily hours allowed |
| AllowWeekends | Yes/No | No | Allow weekend time entry |
| Manager | Person or Group | No | User's manager |
| Department | Single line of text | No | Department |
| IsActive | Yes/No | Yes | Whether user is active |

#### Choice Values for Role:
- Administrator
- ProgramManager
- ProjectManager
- Coordinator
- ProjectObserver
- User

### 5. TimeEntries List (Optional - for detailed tracking)

**List Name:** `TimeEntries`
**Description:** Individual time entries (alternative to JSON storage)

#### Columns:
| Column Name | Type | Required | Description |
|-------------|------|----------|-------------|
| Title | Single line of text | Yes | Auto-generated |
| TimesheetId | Number | Yes | Related timesheet ID |
| ProjectId | Number | Yes | Related project ID |
| ActivityId | Number | No | Related activity ID |
| UserId | Single line of text | Yes | User ID |
| Date | Date and Time | Yes | Entry date |
| Hours | Number | Yes | Hours worked |
| WorkType | Choice | Yes | Type of work |
| Description | Multiple lines of text | No | Work description |
| StartTime | Date and Time | No | Start time |
| EndTime | Date and Time | No | End time |
| IsOvertime | Yes/No | No | Whether this is overtime |

#### Choice Values for WorkType:
- Office
- Onsite
- WFH

## Setup Instructions

### Method 1: Manual Setup (Recommended for initial setup)

1. **Navigate to your SharePoint site**
2. **Go to Site Contents**
3. **Create each list using the "Custom List" template**
4. **Add the required columns as specified above**
5. **Configure choice values for dropdown columns**
6. **Set up permissions as needed**

### Method 2: PowerShell Script (Advanced)

A PowerShell script is provided in the `scripts` folder for automated setup.

### Method 3: SharePoint Framework Feature (Future Enhancement)

The application can be enhanced to include a SharePoint Framework feature that automatically creates the required lists during deployment.

## Permissions Setup

### List-Level Permissions

1. **Projects List:**
   - Administrators: Full Control
   - Program Managers: Contribute
   - Project Managers: Contribute (filtered to their projects)
   - Users: Read

2. **Timesheets List:**
   - Administrators: Full Control
   - Program Managers: Contribute
   - Project Managers: Read (filtered to their team)
   - Users: Contribute (filtered to their own records)

3. **Activities List:**
   - Administrators: Full Control
   - Program Managers: Contribute
   - Project Managers: Contribute (filtered to their projects)
   - Users: Read

4. **UserProfiles List:**
   - Administrators: Full Control
   - Program Managers: Read
   - Project Managers: Read (filtered)
   - Users: Read (own record only)

## Data Validation Rules

### Projects List
- StartDate must be before EndDate (if both are provided)
- HourlyRate and OnsiteRate must be positive numbers
- Status must be one of the defined choices

### Timesheets List
- WeekStartDate must be a Monday
- WeekEndDate must be the Sunday of the same week
- TotalHours must be non-negative
- TimeEntries must be valid JSON

### Activities List
- EstimatedHours and ActualHours must be non-negative
- ProjectId must reference an existing project

### UserProfiles List
- Email must be a valid email format
- HourlyRate and OnsiteRate must be positive numbers
- MaxDailyHours must be between 1 and 24

## Indexing Recommendations

For better performance, create indexes on the following columns:

### Projects List
- Status
- ProjectManager
- Created (default)

### Timesheets List
- UserId
- WeekStartDate
- Status
- Created (default)

### Activities List
- ProjectId
- Status
- AssignedTo

### UserProfiles List
- UserId
- Role
- IsActive

## Backup and Maintenance

1. **Regular Backups:** Ensure regular backups of all lists
2. **Data Cleanup:** Implement data retention policies for old timesheets
3. **Performance Monitoring:** Monitor list performance and optimize as needed
4. **Version Control:** Track changes to list schemas

## Troubleshooting

### Common Issues:
1. **Permission Denied:** Check user permissions on lists
2. **Column Not Found:** Verify all required columns are created
3. **Invalid Data:** Check data validation rules
4. **Performance Issues:** Review indexing and list size

### Support:
For technical support, refer to the main documentation or contact the system administrator.
