# Timesheet 365 - Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying Timesheet 365 to your Microsoft 365 environment.

## Prerequisites
- SharePoint Online tenant with admin permissions
- SharePoint App Catalog configured
- (Optional) Microsoft Teams admin permissions for Teams integration

## Deployment Steps

### 1. Upload to App Catalog
1. Download the latest `time-sheet-tracking-spfx.sppkg` from the releases
2. Navigate to your SharePoint Admin Center
3. Go to "More features" > "Apps" > "App Catalog"
4. Upload the `.sppkg` file to the "Apps for SharePoint" library
5. Click "Deploy" when prompted and check "Make this solution available to all sites in the organization"

### 2. Add to SharePoint Site
1. Navigate to the SharePoint site where you want to add Timesheet 365
2. Go to "Site Contents" > "New" > "App"
3. Search for "Timesheet 365" or "time-sheet-tracking"
4. Click "Add" to install the app

### 3. Add Web Part to Page
1. Navigate to a SharePoint page where you want the timesheet
2. Edit the page
3. Click "+" to add a web part
4. Search for "QTS Timesheet Tracker"
5. Add the web part to your page
6. Save the page

### 4. Teams Integration (Optional)
1. Open Microsoft Teams
2. Navigate to the team/channel where you want to add Timesheet 365
3. Click "+" to add a tab
4. Search for "SharePoint" and select it
5. Choose the page containing your Timesheet 365 web part

## Initial Configuration

### 1. First-Time Setup
- The first user to access Timesheet 365 should be a SharePoint administrator
- Required SharePoint lists will be created automatically:
  - **Projects**: Stores project information
  - **Timesheets**: Stores timesheet data

### 2. User Role Configuration
User roles are managed through the application. Administrators can assign roles:
- **Administrator**: Full system access
- **Program Manager**: Multi-project management
- **Project Manager**: Project-specific management
- **Coordinator**: Project assistance role
- **Project Observer**: Read-only project access
- **User**: Basic timesheet entry

### 3. Project Setup
1. Login as Administrator or Program Manager
2. Navigate to "Project Management" tab
3. Click "New Project" to create your first project
4. Configure project details, team members, and rates

## Permissions

### SharePoint Permissions Required
- **Read**: All users need read access to the site
- **Contribute**: Users need contribute access to add/edit their timesheets
- **Edit**: Project managers need edit access for project management
- **Full Control**: Administrators need full control for system configuration

### Teams Permissions
- Teams members automatically inherit appropriate permissions based on their SharePoint access

## Troubleshooting

### Common Issues
1. **Web part not appearing**: Ensure the app is properly deployed and added to the site
2. **Access denied**: Check SharePoint permissions for the user
3. **Lists not created**: Ensure the user has sufficient permissions to create lists
4. **Teams integration issues**: Verify SharePoint permissions and Teams app policies

### Support
- Check the project documentation in the `docs/` folder
- Review SharePoint ULS logs for detailed error information
- Verify browser compatibility (Chrome, Edge, Firefox, Safari supported)

## Security Considerations
- All data remains within your Microsoft 365 tenant
- Uses SharePoint's native security model
- No external data storage or third-party services
- Regular backup through SharePoint's built-in mechanisms

## Performance Optimization
- Regularly archive old timesheet data
- Monitor SharePoint list thresholds (5000 items)
- Consider using SharePoint Online indexing for large datasets
- Enable CDN for faster asset loading