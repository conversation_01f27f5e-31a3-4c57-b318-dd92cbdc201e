# Timesheet 365 - User Guide

Welcome to Timesheet 365, a comprehensive time tracking solution built for SharePoint. This guide will help you get started and make the most of the application's features.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Time Tracking](#time-tracking)
3. [Project Management](#project-management)
4. [Reports and Analytics](#reports-and-analytics)
5. [Approval Workflow](#approval-workflow)
6. [User Management](#user-management)
7. [Mobile Usage](#mobile-usage)
8. [Tips and Best Practices](#tips-and-best-practices)

## Getting Started

### Accessing Timesheet 365

1. Navigate to your SharePoint site
2. Find the page containing the Timesheet 365 web part
3. The application will load automatically
4. You'll see different tabs based on your user role

### User Roles and Permissions

**Administrator**
- Full access to all features
- User management and system configuration
- Access to all reports and data

**Program Manager**
- Manage projects and users
- Access to comprehensive reports
- Approve timesheets across projects

**Project Manager**
- Manage assigned projects
- Approve timesheets for team members
- Access to project-specific reports

**Coordinator**
- Coordinate project activities
- Limited project management access
- View team timesheets

**Project Observer**
- View project information
- Read-only access to reports
- No editing capabilities

**User**
- Enter and submit timesheets
- View own timesheet history
- Basic reporting access

## Time Tracking

### Overview Tab

The main dashboard shows:
- Current week timesheet
- Quick summary of hours
- Recent activity
- Pending approvals (if applicable)

### Time Entry Methods

#### 1. Grid Entry (Recommended for bulk entry)

1. Click on the **Time Tracking** tab
2. Use the timesheet grid to enter hours
3. Select project and work type for each row
4. Enter hours for each day of the week
5. Add descriptions as needed
6. Hours are automatically saved

**Grid Features:**
- Add multiple projects per week
- Different work types (Office, Onsite, WFH)
- Real-time total calculations
- Validation for maximum daily hours

#### 2. Quick Time Entry

1. Click **Quick Entry** button
2. Select project and work type
3. Choose date
4. Enter hours or use time range
5. Add description
6. Click **Save Entry**

**Best for:**
- Single time entries
- Corrections and adjustments
- Mobile usage

#### 3. Stopwatch Timer

1. Click **Stopwatch** button
2. Select project and work type
3. Add description (optional)
4. Click **Start Timer**
5. Use **Pause/Resume** as needed
6. Click **Stop & Save** when done

**Best for:**
- Real-time tracking
- Accurate time measurement
- Task-based work

### Work Types

**Office**: Regular office work
**Onsite**: Work performed at client location
**WFH**: Work from home

Different work types may have different hourly rates configured by your administrator.

### Time Entry Tips

- **Daily Entry**: Enter time daily for accuracy
- **Descriptions**: Add meaningful descriptions for better tracking
- **Validation**: System validates against maximum daily hours
- **Weekends**: Weekend entry may be restricted based on settings

## Project Management

### Viewing Projects

1. Go to **Projects** tab
2. View list of available projects
3. See project status, manager, and team information
4. Click on projects for detailed information

### Creating Projects (Manager+ roles)

1. Click **New Project**
2. Fill in required information:
   - Project name and description
   - Client and company details
   - Start and end dates
   - Hourly rates
   - Project manager assignment
3. Save the project

### Project Features

- **Status Tracking**: Active, Completed, On Hold, Cancelled
- **Rate Management**: Different rates for office and onsite work
- **Team Assignment**: Assign team members to projects
- **Activity Management**: Break projects into activities/tasks

### Bulk Project Upload

Administrators can upload multiple projects via CSV:
1. Click **Bulk Upload**
2. Download CSV template
3. Fill in project information
4. Upload completed CSV file

## Reports and Analytics

### Report Types

#### Overview Reports
- Summary cards with key metrics
- Cost analysis and breakdowns
- Work type distribution

#### Timesheet Reports
- Individual timesheet details
- Team timesheet summaries
- Approval status tracking

#### Project Reports
- Project-specific time tracking
- Cost analysis per project
- Team productivity metrics

### Generating Reports

1. Go to **Reports** tab
2. Select report type and filters:
   - Date range
   - Projects
   - Users
   - Status filters
3. View results in the interface
4. Export data as needed

### Export Options

- **CSV**: For Excel analysis
- **JSON**: For system integration
- **PDF**: For formal reporting (planned)

### Cost Analysis

The system automatically calculates:
- Total hours and costs
- Work type breakdowns
- Project profitability
- Rate comparisons

## Approval Workflow

### Submitting Timesheets

1. Complete your timesheet for the week
2. Review all entries for accuracy
3. Click **Submit Timesheet**
4. Timesheet status changes to "Submitted"
5. Wait for manager approval

### Approval Process (Managers)

1. Go to **Approvals** tab
2. Review pending timesheets
3. Click **View Details** to see full timesheet
4. **Approve** or **Reject** with comments
5. User receives notification of decision

### Approval Features

- **Bulk Actions**: Approve multiple timesheets
- **Detailed Review**: View cost summaries and breakdowns
- **Rejection Comments**: Provide feedback for corrections
- **Audit Trail**: Track all approval actions

### Post-Approval Editing

- Approved timesheets are locked from user editing
- Administrators can make post-approval corrections
- Changes are tracked in audit log

## User Management

### Managing Users (Admin/Program Manager)

1. Go to **Users** tab
2. View current user list
3. Add new users with **New User** button
4. Edit existing users by clicking **Edit**
5. Assign appropriate roles and permissions

### User Configuration

- **Basic Information**: Name, email, department
- **Role Assignment**: Select appropriate user role
- **Rate Settings**: User-specific hourly rates
- **Preferences**: Max daily hours, weekend permissions
- **Status**: Active/inactive user status

## Mobile Usage

### Mobile Features

Timesheet 365 is fully responsive and optimized for mobile devices:

- **Touch-Friendly Interface**: Large buttons and touch targets
- **Responsive Design**: Adapts to screen size
- **Quick Entry**: Optimized for mobile time entry
- **Offline Capability**: Basic functionality works offline

### Mobile Best Practices

- Use **Quick Entry** for single time entries
- **Stopwatch** works well for real-time tracking
- Review entries on desktop for detailed analysis
- Submit timesheets from any device

### Mobile Limitations

- Complex grid editing better on desktop
- Detailed reports optimized for larger screens
- Bulk operations require desktop access

## Tips and Best Practices

### For Users

1. **Daily Entry**: Enter time daily for best accuracy
2. **Detailed Descriptions**: Help with project tracking and billing
3. **Regular Submission**: Submit timesheets weekly
4. **Mobile App**: Use mobile for quick entries and real-time tracking

### For Project Managers

1. **Regular Review**: Check team timesheets regularly
2. **Quick Approval**: Approve timesheets promptly
3. **Feedback**: Provide clear rejection reasons
4. **Project Monitoring**: Use reports to track project progress

### For Administrators

1. **User Training**: Ensure all users understand the system
2. **Regular Maintenance**: Clean up old data and inactive users
3. **Permission Review**: Regularly review user roles and permissions
4. **Backup**: Maintain regular backups of timesheet data

### Data Quality

- **Consistent Descriptions**: Use standardized task descriptions
- **Accurate Time**: Round to appropriate increments (15 minutes)
- **Project Selection**: Ensure correct project assignment
- **Work Type**: Select appropriate work type for rate calculation

## Troubleshooting

### Common Issues

**Cannot see timesheet data**
- Check user permissions
- Verify project assignments
- Contact administrator

**Time entries not saving**
- Check internet connection
- Verify maximum daily hours not exceeded
- Refresh page and try again

**Cannot submit timesheet**
- Ensure all required fields completed
- Check for validation errors
- Verify timesheet not already submitted

**Mobile display issues**
- Clear browser cache
- Update browser to latest version
- Try different browser

### Getting Help

1. **User Guide**: Refer to this documentation
2. **Administrator**: Contact your system administrator
3. **IT Support**: Contact IT support for technical issues
4. **Training**: Request additional training if needed

## Keyboard Shortcuts

- **Tab**: Navigate between fields
- **Enter**: Save current entry
- **Esc**: Cancel current operation
- **Ctrl+S**: Save timesheet (where applicable)

## Integration Features

### Power BI (Planned)
- Advanced analytics and dashboards
- Real-time data visualization
- Custom report creation

### Email Notifications
- Timesheet submission confirmations
- Approval/rejection notifications
- Weekly reminders

### Calendar Integration (Planned)
- Sync with Outlook calendar
- Automatic time entry suggestions
- Meeting time tracking

---

For additional support or feature requests, please contact your system administrator or IT support team.
